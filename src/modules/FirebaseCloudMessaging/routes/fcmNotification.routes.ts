import { Router } from 'express';
import {
  registerNotificationToken,
  sendNotificationToAssociateById,
  unregisterNotificationToken,
  updateNotificationTokenState,
} from '../controllers/fcmNotification.controller';

const fcmNotificationRouter = Router();

fcmNotificationRouter.post('/register', registerNotificationToken);
fcmNotificationRouter.post('/unregister', unregisterNotificationToken);
fcmNotificationRouter.patch('/updateTokenState', updateNotificationTokenState);
fcmNotificationRouter.post('/send', sendNotificationToAssociateById);

export default fcmNotificationRouter;
