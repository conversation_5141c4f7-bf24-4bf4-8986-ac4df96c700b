import z from 'zod';

export const fcmTokenDto = z.object({
  fcmToken: z.string(),
  associateId: z.string(),
  deviceDetails: z.record(z.string(), z.any()),
});

export const fcmTokenStatusUpdateDto = z.object({
  fcmToken: z.string(),
  associateId: z.string(),
  isActive: z.boolean(),
});

export const fcmNotificationPayloadDto = z.object({
  associateId: z.string(),
  payload: z.object({
    title: z.string(),
    body: z.string(),
    data: z.record(z.string(), z.any()).optional(),
  }),
});
