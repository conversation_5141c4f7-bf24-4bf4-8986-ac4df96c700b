import { logger } from '@/clean/lib/logger';
import { firebaseAdmin } from '../configuration/firebaseAdmin';
import { NotificationPayload } from '../interfaces/fcmNotificationPayload';
import fcmSchema from '@/models/fcmSchema';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { Message } from 'firebase-admin/messaging';

class FCMNotificationService {
  async registerNotificationToken(
    associateId: String,
    fcmToken: String,
    deviceDetails: Record<string, any>
  ): Promise<void> {
    try {
      // Check if the token already exists for the associateId
      const fcmNotificationDetails = await fcmSchema.findOne({ associateId }).lean();
      // If no existing tokens are found, create a new entry
      if (!fcmNotificationDetails) {
        logger.info(
          `[registerNotificationToken] - No existing FCM tokens found for associateId: ${associateId}`
        );
        // Create a new FCM token entry
        await fcmSchema.create({
          associateId,
          fcmTokens: [
            {
              token: fcmToken,
              isActive: true,
              deviceDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          ],
        });
        // Log the successful registration
        logger.info(`[registerNotificationToken] - FCM token registered for associateId: ${associateId}`);
      } else {
        // If existing tokens are found, check if the token already exists
        const existingToken = fcmNotificationDetails.fcmTokens.find((token) => token.token === fcmToken);
        // If the token already exists and is active, log and throw an AlreadyExistException
        if (existingToken) {
          if (existingToken.isActive) {
            logger.info(
              `[registerNotificationToken] - FCM token already registered for associateId: ${associateId}`
            );
            throw new AlreadyExistException();
          } else {
            // If the token exists but is inactive, update it to active
            await fcmSchema.updateOne(
              { associateId, 'fcmTokens.token': fcmToken },
              {
                $set: {
                  'fcmTokens.$.isActive': true,
                  'fcmTokens.$.updatedAt': new Date(),
                },
              }
            );
            // Log the successful reactivation
            logger.info(
              `[registerNotificationToken] - FCM token reactivated for associateId: ${associateId}`
            );
          }
        } else {
          // If the token does not exist, add it to the existing tokens
          await fcmSchema.updateOne(
            { associateId },
            {
              $push: {
                fcmTokens: {
                  token: fcmToken,
                  isActive: true,
                  deviceDetails,
                  createdAt: new Date(),
                  updatedAt: new Date(),
                },
              },
            }
          );
          // Log the successful registration
          logger.info(`[registerNotificationToken] - FCM token added for associateId: ${associateId}`);
        }
      }
    } catch (error) {
      logger.error('[registerNotificationToken] - Error registering FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async unregisterNotificationToken(associateId: String, fcmToken: String): Promise<void> {
    try {
      // Remove the FCM token for the associateId
      const result = await fcmSchema.updateOne(
        { associateId, 'fcmTokens.token': fcmToken },
        { $pull: { fcmTokens: { token: fcmToken } } }
      );
      // 2. Remove token entity if fcmTokens is now empty
      await fcmSchema.deleteOne({
        associateId,
        fcmTokens: { $size: 0 },
      });
      // Check if any tokens were modified
      if (result.modifiedCount > 0) {
        logger.info(`[unregisterNotificationToken] - FCM token unregistered for associateId: ${associateId}`);
      } else {
        logger.info(
          `[unregisterNotificationToken] - No FCM token found to unregister for associateId: ${associateId}`
        );
        throw new NotFoundException({ code: '404', message: 'FCM token not found for the associateId.' });
      }
    } catch (error) {
      logger.error('[unregisterNotificationToken] - Error unregistering FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async updateNotificationTokenState(
    associateId: String,
    fcmToken: String,
    isActive: Boolean
  ): Promise<void> {
    try {
      // Update the FCM token to set it as inactive
      const result = await fcmSchema.updateOne(
        { associateId, 'fcmTokens.token': fcmToken },
        { $set: { 'fcmTokens.$.isActive': isActive } }
      );
      // Check if any tokens were modified
      if (result.modifiedCount > 0) {
        logger.info(
          `[updateNotificationTokenState] - FCM token state is ${isActive} for associateId: ${associateId}`
        );
      } else {
        logger.info(
          `[updateNotificationTokenState] - No FCM token found to update state to ${isActive} for associateId: ${associateId}`
        );
        throw new NotFoundException({ code: '404', message: 'FCM token not found for the associateId.' });
      }
    } catch (error) {
      logger.error('[updateNotificationTokenState] - Error updating the state of FCM token:', error);
      throw error; // Re-throw the error to be handled by the caller
    }
  }

  async sendNotificationToAssociateById(id: String, payload: NotificationPayload): Promise<void> {
    const fcmNotificationDetails = await fcmSchema.findOne({ associateId: id }).lean();
    if (!fcmNotificationDetails) {
      logger.error(`[sendNotificationToAssociateById] - No FCM token found for associateId: ${id}`);
      throw new NotFoundException({ code: '404', message: 'FCM token not found for the associateId.' });
    }
    // Extract the first active FCM token
    const activeTokens = fcmNotificationDetails.fcmTokens.filter((token) => token.isActive);
    if (activeTokens.length === 0) {
      logger.error(`[sendNotificationToAssociateById] - No active FCM tokens found for associateId: ${id}`);
      throw new NotFoundException({
        code: '1010',
        message: 'No active FCM tokens found for the associateId.',
      });
    }
    const messages: Message[] = [];
    for (const token of activeTokens) {
      messages.push({
        token: token.token,
        notification: {
          title: payload.title,
          body: payload.body,
        },
        data: payload.data,
      });
    }
    try {
      const response = await firebaseAdmin.messaging().sendEach(messages);
      // Log the response from FCM
      response.responses.forEach((resp, idx) => {
        logger.info(
          `[sendNotification] - Message status success:${resp.success} and device details are ${fcmNotificationDetails.fcmTokens[idx].deviceDetails}`
        );
      });
      logger.info('[sendNotification] - Notification sent successfully');
    } catch (error) {
      logger.error('[sendNotification] - Error sending notification:', error);
    }
  }
}

// Exporting the FCMNotificationService instance for use in other parts of the application
export const fcmNotificationService = new FCMNotificationService();
