import { Request, Response } from 'express';
import { fcmNotificationPayloadDto, fcmTokenDto, fcmTokenStatusUpdateDto } from '../dto/fcmToken.dto';
import { fcmNotificationService } from '../services/fcmNotification.service';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { z } from 'zod';
import { NotificationPayload } from '../interfaces/fcmNotificationPayload';

export const registerNotificationToken = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmTokenDto.parse(req.body);
    await fcmNotificationService.registerNotificationToken(
      validatedData.associateId,
      validatedData.fcmToken,
      validatedData.deviceDetails
    );
    res.status(200).send({ message: 'FCM token registered successfully.' });
  } catch (error) {
    // Handle validation errors or other errors
    if (error instanceof z.ZodError) {
      res.status(400).send({ error: 'Invalid request data.', code: 1009, message: error });
    } else if (error instanceof AlreadyExistException) {
      res.status(409).send({
        code: 1008,
        error: 'AlreadyExists',
        message: 'Resource already exists',
      });
    } else {
      res.status(500).send({ error: 'Error registering FCM token.', code: 500, message: error });
    }
  }
};

export const unregisterNotificationToken = async (req: Request, res: Response) => {
  try {
    const { associateId, fcmToken } = req.body;
    await fcmNotificationService.unregisterNotificationToken(associateId, fcmToken);
    res.status(200).send({ message: 'FCM token unregistered successfully.' });
  } catch (error) {
    if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).send({ error: 'Error unregistering FCM token.' });
    }
  }
};

export const updateNotificationTokenState = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmTokenStatusUpdateDto.parse(req.body);
    await fcmNotificationService.updateNotificationTokenState(
      validatedData.associateId,
      validatedData.fcmToken,
      validatedData.isActive
    );
    res.status(200).json({ message: 'FCM token inactivated successfully.' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).send({ error: 'Invalid request data.', code: 1009, message: error });
    } else if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).json({ error: 'Error inactivating FCM token.' });
    }
  }
};

export const sendNotificationToAssociateById = async (req: Request, res: Response) => {
  try {
    const validatedData = fcmNotificationPayloadDto.parse(req.body);
    const payload: NotificationPayload = {
      title: validatedData.payload.title,
      body: validatedData.payload.body,
      data: validatedData.payload.data,
    };
    await fcmNotificationService.sendNotificationToAssociateById(validatedData.associateId, payload);
    res.status(200).json({ message: 'Notification sent successfully.' });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).send({ error: 'Invalid request data.', code: 1009, message: error });
    } else if (error instanceof NotFoundException) {
      res.status(404).send({ error: 'FCM token not found for the associateId.', code: 404, message: error });
    } else {
      res.status(500).json({ error: 'Error sending notification.' });
    }
  }
};
