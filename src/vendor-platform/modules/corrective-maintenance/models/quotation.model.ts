import { Schema, Document, Types } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

export enum QuotationStatus {
  DRAFT = 'draft',
  PENDING_APPROVAL = 'pending-approval',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  PARTIALLY_APPROVED = 'partially-approved',
}

export interface IQuotationService {
  serviceId: Types.ObjectId;
  serviceName: string;
  description: string;
  estimatedCost: number;
  estimatedDuration: number; // in hours
  laborCost: number;
  partsCost: number;
  isApproved: boolean;
  rejectionReason?: string;

  // Parts breakdown
  parts: {
    name: string;
    quantity: number;
    unitCost: number;
    totalCost: number;
    estimatedArrival?: Date;
  }[];

  // SLA information
  slaEstimate: number; // hours
  partsAvailabilityDelay?: number; // additional hours if parts not available
}

export interface IQuotation extends Document {
  _id: Types.ObjectId;
  orderId: Types.ObjectId;

  // Quotation details
  quotationNumber: string;
  status: QuotationStatus;
  version: number; // for tracking revisions

  // Services included in quotation
  services: IQuotationService[];

  // Totals
  totalEstimatedCost: number;
  totalEstimatedDuration: number; // in hours
  totalLaborCost: number;
  totalPartsCost: number;

  // Approval workflow
  approvalRequired: boolean;
  approvalType: 'fleet' | 'customer';
  approverEmail?: string;

  // Individual service approvals
  approvedServices: Types.ObjectId[];
  rejectedServices: Types.ObjectId[];

  // Timing
  validUntil: Date;
  submittedAt?: Date;
  approvedAt?: Date;
  rejectedAt?: Date;

  // SLA and scheduling
  overallSLA: number; // total hours including parts delays
  earliestStartDate: Date;
  estimatedCompletionDate: Date;

  // Financial terms
  paymentTerms?: string;
  warrantyTerms?: string;

  // Communication
  customerNotes?: string;
  internalNotes?: string;

  // Evidence and documentation
  diagnosticEvidence: {
    photos: string[];
    videos: string[];
    reports: string[];
  };

  createdAt: Date;
  updatedAt: Date;
}

const QuotationServiceSchema = new Schema<IQuotationService>(
  {
    serviceId: { type: Schema.Types.ObjectId, ref: 'CorrectiveService', required: true },
    serviceName: { type: String, required: true },
    description: { type: String, required: true },
    estimatedCost: { type: Number, required: true, min: 0 },
    estimatedDuration: { type: Number, required: true, min: 0 },
    laborCost: { type: Number, required: true, min: 0 },
    partsCost: { type: Number, required: true, min: 0 },
    isApproved: { type: Boolean, default: false },
    rejectionReason: { type: String },

    parts: [
      {
        _id: false,
        name: { type: String, required: true },
        quantity: { type: Number, required: true, min: 1 },
        unitCost: { type: Number, required: true, min: 0 },
        totalCost: { type: Number, required: true, min: 0 },
        estimatedArrival: { type: Date },
      },
    ],

    slaEstimate: { type: Number, required: true, min: 0 },
    partsAvailabilityDelay: { type: Number, min: 0 },
  },
  { _id: false }
);

const QuotationSchema = new Schema<IQuotation>(
  {
    orderId: {
      type: Schema.Types.ObjectId,
      ref: 'CorrectiveMaintenanceOrder',
      required: true,
      index: true,
    },

    quotationNumber: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    status: {
      type: String,
      enum: Object.values(QuotationStatus),
      default: QuotationStatus.DRAFT,
      index: true,
    },
    version: { type: Number, default: 1, min: 1 },

    services: [QuotationServiceSchema],

    totalEstimatedCost: { type: Number, required: true, min: 0 },
    totalEstimatedDuration: { type: Number, required: true, min: 0 },
    totalLaborCost: { type: Number, required: true, min: 0 },
    totalPartsCost: { type: Number, required: true, min: 0 },

    approvalRequired: { type: Boolean, default: true },
    approvalType: { type: String, enum: ['fleet', 'customer'], required: true },
    approverEmail: { type: String },

    approvedServices: [{ type: Schema.Types.ObjectId, ref: 'CorrectiveService' }],
    rejectedServices: [{ type: Schema.Types.ObjectId, ref: 'CorrectiveService' }],

    validUntil: { type: Date, required: true },
    submittedAt: { type: Date },
    approvedAt: { type: Date },
    rejectedAt: { type: Date },

    overallSLA: { type: Number, required: true, min: 0 },
    earliestStartDate: { type: Date, required: true },
    estimatedCompletionDate: { type: Date, required: true },

    paymentTerms: { type: String },
    warrantyTerms: { type: String },

    customerNotes: { type: String },
    internalNotes: { type: String },

    diagnosticEvidence: {
      photos: [{ type: String }],
      videos: [{ type: String }],
      reports: [{ type: String }],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
QuotationSchema.index({ orderId: 1, version: -1 });
QuotationSchema.index({ status: 1, validUntil: 1 });
QuotationSchema.index({ approvalType: 1, status: 1 });

// Pre-save middleware to calculate totals
QuotationSchema.pre('save', function (next) {
  if (this.services && this.services.length > 0) {
    this.totalEstimatedCost = this.services.reduce((total, service) => total + service.estimatedCost, 0);
    this.totalEstimatedDuration = this.services.reduce(
      (total, service) => total + service.estimatedDuration,
      0
    );
    this.totalLaborCost = this.services.reduce((total, service) => total + service.laborCost, 0);
    this.totalPartsCost = this.services.reduce((total, service) => total + service.partsCost, 0);

    // Calculate overall SLA including parts delays
    this.overallSLA = this.services.reduce((max, service) => {
      const serviceTime = service.slaEstimate + (service.partsAvailabilityDelay || 0);
      return Math.max(max, serviceTime);
    }, 0);
  }
  next();
});

// Generate quotation number - use pre('validate') to run before validation
QuotationSchema.pre('validate', async function (next) {
  if (this.isNew && !this.quotationNumber) {
    try {
      // Get the model from this.constructor to avoid circular reference issues
      const QuotationModel = this.constructor as any;
      const count = await QuotationModel.countDocuments();

      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const sequentialNumber = String(count + 1).padStart(6, '0');

      this.quotationNumber = `COT-${year}${month}-${sequentialNumber}`;
    } catch (error) {
      console.error('Error generating quotation number:', error);
      return next(error as Error);
    }
  }
  next();
});

export const Quotation = vendorDB.model<IQuotation>('Quotation', QuotationSchema);
