import { Schema, Document, Types } from 'mongoose';
import vendorDB from '@/vendor-platform/db';
import { IWorkshop } from '../../workshop/models/workshops.model';
import { IOrganization } from '../../organizations/models/organization.model';

export enum CorrectiveMaintenanceStatus {
  PENDING = 'pending',
  DIAGNOSED = 'diagnosed',
  QUOTED = 'quoted',
  APPROVED = 'approved',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  WAITING_FOR_PARTS = 'waiting-for-parts',
}

export enum CorrectiveMaintenanceType {
  CUSTOMER_INITIATED = 'customer-initiated',
  PREVENTIVE_DETECTED = 'preventive-detected',
}

export enum VehicleArrivalMethod {
  DRIVING = 'driving',
  TOW_TRUCK = 'tow-truck',
}

export enum FailureType {
  KNOWN = 'known', // brakes, tires, suspension, clutch, etc.
  UNKNOWN = 'unknown', // requires diagnosis
}

export interface ICorrectiveMaintenanceOrder extends Document {
  _id: Types.ObjectId;
  stockId: Types.ObjectId;
  associateId: Types.ObjectId;
  organizationId: Types.ObjectId;
  workshopId: Types.ObjectId;

  // Virtual populated fields
  workshop: IWorkshop;
  organization: IOrganization;
  stockVehicle: any; // StockVehicle interface
  associate: any; // Associate interface

  // Order details
  type: CorrectiveMaintenanceType;
  status: CorrectiveMaintenanceStatus;
  failureType: FailureType;
  arrivalMethod: VehicleArrivalMethod;

  // Customer/Fleet information
  customerDescription?: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;

  // Diagnosis information
  diagnosisCompleted: boolean;
  diagnosisDate?: Date;
  diagnosisNotes?: string;
  diagnosisEvidence: {
    photos: string[];
    videos: string[];
  };

  // Services and quotation
  services: Types.ObjectId[]; // Reference to CorrectiveService
  totalEstimatedCost: number;
  totalEstimatedDuration: number; // in hours
  totalActualCost?: number;
  totalActualDuration?: number;

  // Approval workflow
  requiresApproval: boolean;
  approvalType: 'fleet' | 'customer';
  approvedBy?: string; // email or user ID
  approvedAt?: Date;
  rejectionReason?: string;

  // Scheduling
  scheduledDate?: Date;
  startDate?: Date;
  completionDate?: Date;

  // Parts management
  pendingParts: {
    partName: string;
    estimatedArrival: Date;
    supplier: string;
    cost: number;
  }[];

  // Vehicle status during service
  vehicleAtWorkshop: boolean;
  vehicleCanLeave: boolean; // if waiting for parts

  // SLA tracking
  slaTarget: Date;
  slaActual?: Date;
  slaCompliance: boolean;

  // Evidence and documentation
  beforePhotos: string[];
  afterPhotos: string[];
  completionEvidence: string[];

  // Notes and communication
  internalNotes: string[];
  customerNotes: string[];

  createdAt: Date;
  updatedAt: Date;
}

const CorrectiveMaintenanceOrderSchema = new Schema<ICorrectiveMaintenanceOrder>(
  {
    stockId: { type: Schema.Types.ObjectId, ref: 'StockVehicle', required: true, index: true },
    associateId: { type: Schema.Types.ObjectId, ref: 'Associate', required: true, index: true },
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    workshopId: { type: Schema.Types.ObjectId, ref: 'Workshop', required: true },

    type: {
      type: String,
      enum: Object.values(CorrectiveMaintenanceType),
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(CorrectiveMaintenanceStatus),
      default: CorrectiveMaintenanceStatus.PENDING,
      index: true,
    },
    failureType: {
      type: String,
      enum: Object.values(FailureType),
      required: true,
    },
    arrivalMethod: {
      type: String,
      enum: Object.values(VehicleArrivalMethod),
      required: true,
    },

    customerDescription: { type: String },
    canVehicleDrive: { type: Boolean, required: true },
    needsTowTruck: { type: Boolean, required: true },

    diagnosisCompleted: { type: Boolean, default: false },
    diagnosisDate: { type: Date },
    diagnosisNotes: { type: String },
    diagnosisEvidence: {
      photos: [{ type: String }],
      videos: [{ type: String }],
    },

    services: [{ type: Schema.Types.ObjectId, ref: 'CorrectiveService' }],
    totalEstimatedCost: { type: Number, default: 0 },
    totalEstimatedDuration: { type: Number, default: 0 },
    totalActualCost: { type: Number },
    totalActualDuration: { type: Number },

    requiresApproval: { type: Boolean, default: true },
    approvalType: { type: String, enum: ['fleet', 'customer'], required: true },
    approvedBy: { type: String },
    approvedAt: { type: Date },
    rejectionReason: { type: String },

    scheduledDate: { type: Date },
    startDate: { type: Date },
    completionDate: { type: Date },

    pendingParts: [
      {
        _id: false,
        partName: { type: String, required: true },
        estimatedArrival: { type: Date, required: true },
        supplier: { type: String, required: true },
        cost: { type: Number, required: true },
      },
    ],

    vehicleAtWorkshop: { type: Boolean, default: false },
    vehicleCanLeave: { type: Boolean, default: true },

    slaTarget: { type: Date, required: true },
    slaActual: { type: Date },
    slaCompliance: { type: Boolean, default: true },

    beforePhotos: [{ type: String }],
    afterPhotos: [{ type: String }],
    completionEvidence: [{ type: String }],

    internalNotes: [{ type: String }],
    customerNotes: [{ type: String }],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for performance
CorrectiveMaintenanceOrderSchema.index({ stockId: 1, status: 1 });
CorrectiveMaintenanceOrderSchema.index({ organizationId: 1, status: 1 });
CorrectiveMaintenanceOrderSchema.index({ workshopId: 1, status: 1 });
CorrectiveMaintenanceOrderSchema.index({ createdAt: -1 });

// Virtual population
CorrectiveMaintenanceOrderSchema.virtual('workshop', {
  ref: 'Workshop',
  localField: 'workshopId',
  foreignField: '_id',
  justOne: true,
});

CorrectiveMaintenanceOrderSchema.virtual('organization', {
  ref: 'Organization',
  localField: 'organizationId',
  foreignField: '_id',
  justOne: true,
});

CorrectiveMaintenanceOrderSchema.virtual('stockVehicle', {
  ref: 'StockVehicle',
  localField: 'stockId',
  foreignField: '_id',
  justOne: true,
});

CorrectiveMaintenanceOrderSchema.virtual('associate', {
  ref: 'Associate',
  localField: 'associateId',
  foreignField: '_id',
  justOne: true,
});

export const CorrectiveMaintenanceOrder = vendorDB.model<ICorrectiveMaintenanceOrder>(
  'CorrectiveMaintenanceOrder',
  CorrectiveMaintenanceOrderSchema
);
