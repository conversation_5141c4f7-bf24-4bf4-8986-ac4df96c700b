import { Schema, Document, Types } from 'mongoose';
import vendorDB from '@/vendor-platform/db';

export enum PartAvailabilityStatus {
  AVAILABLE = 'available',
  LOW_STOCK = 'low-stock',
  OUT_OF_STOCK = 'out-of-stock',
  PENDING_ORDER = 'pending-order',
  DISCONTINUED = 'discontinued',
}

export enum PartCategory {
  BRAKES = 'brakes',
  TIRES = 'tires',
  SUSPENSION = 'suspension',
  ENGINE = 'engine',
  TRANSMISSION = 'transmission',
  ELECTRICAL = 'electrical',
  BODYWORK = 'bodywork',
  CLUTCH = 'clutch',
  COOLING_SYSTEM = 'cooling-system',
  EXHAUST = 'exhaust',
  FUEL_SYSTEM = 'fuel-system',
  STEERING = 'steering',
  FILTERS = 'filters',
  FLUIDS = 'fluids',
  OTHER = 'other',
}

export interface IInventoryPart extends Document {
  _id: Types.ObjectId;
  organizationId: Types.ObjectId;
  workshopId?: Types.ObjectId;

  // Part identification
  partNumber: string;
  name: string;
  description?: string;
  category: PartCategory;
  brand?: string;
  model?: string;
  
  // Compatibility
  compatibleVehicles: {
    brand: string;
    model: string;
    years: number[];
    versions?: string[];
  }[];

  // Inventory details
  currentStock: number;
  minimumStock: number;
  maximumStock: number;
  reservedStock: number; // Stock reserved for pending orders
  availableStock: number; // currentStock - reservedStock
  
  // Pricing
  unitCost: number;
  sellingPrice: number;
  currency: string;
  
  // Supplier information
  suppliers: {
    supplierId: string;
    supplierName: string;
    supplierPartNumber?: string;
    leadTime: number; // days
    minimumOrderQuantity: number;
    unitCost: number;
    isPrimary: boolean;
  }[];
  
  // Status and availability
  status: PartAvailabilityStatus;
  isActive: boolean;
  
  // Location
  location?: {
    warehouse?: string;
    shelf?: string;
    bin?: string;
  };
  
  // Timestamps
  lastRestocked?: Date;
  lastOrdered?: Date;
  nextExpectedDelivery?: Date;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

const CompatibleVehicleSchema = new Schema({
  brand: { type: String, required: true },
  model: { type: String, required: true },
  years: [{ type: Number, required: true }],
  versions: [{ type: String }],
}, { _id: false });

const SupplierSchema = new Schema({
  supplierId: { type: String, required: true },
  supplierName: { type: String, required: true },
  supplierPartNumber: { type: String },
  leadTime: { type: Number, required: true, min: 0 },
  minimumOrderQuantity: { type: Number, required: true, min: 1 },
  unitCost: { type: Number, required: true, min: 0 },
  isPrimary: { type: Boolean, default: false },
}, { _id: false });

const LocationSchema = new Schema({
  warehouse: { type: String },
  shelf: { type: String },
  bin: { type: String },
}, { _id: false });

const InventoryPartSchema = new Schema<IInventoryPart>(
  {
    organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true },
    workshopId: { type: Schema.Types.ObjectId, ref: 'Workshop' },

    // Part identification
    partNumber: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String },
    category: { type: String, enum: Object.values(PartCategory), required: true },
    brand: { type: String },
    model: { type: String },
    
    // Compatibility
    compatibleVehicles: [CompatibleVehicleSchema],

    // Inventory details
    currentStock: { type: Number, required: true, min: 0, default: 0 },
    minimumStock: { type: Number, required: true, min: 0, default: 0 },
    maximumStock: { type: Number, required: true, min: 0 },
    reservedStock: { type: Number, default: 0, min: 0 },
    availableStock: { type: Number, default: 0, min: 0 },
    
    // Pricing
    unitCost: { type: Number, required: true, min: 0 },
    sellingPrice: { type: Number, required: true, min: 0 },
    currency: { type: String, default: 'MXN' },
    
    // Supplier information
    suppliers: [SupplierSchema],
    
    // Status and availability
    status: { type: String, enum: Object.values(PartAvailabilityStatus), default: PartAvailabilityStatus.AVAILABLE },
    isActive: { type: Boolean, default: true },
    
    // Location
    location: LocationSchema,
    
    // Timestamps
    lastRestocked: { type: Date },
    lastOrdered: { type: Date },
    nextExpectedDelivery: { type: Date },
  },
  {
    timestamps: true,
    collection: 'inventory_parts',
  }
);

// Indexes for better performance
InventoryPartSchema.index({ organizationId: 1, partNumber: 1 });
InventoryPartSchema.index({ organizationId: 1, category: 1 });
InventoryPartSchema.index({ organizationId: 1, status: 1 });
InventoryPartSchema.index({ 'compatibleVehicles.brand': 1, 'compatibleVehicles.model': 1 });

// Pre-save middleware to calculate available stock
InventoryPartSchema.pre('save', function(next) {
  this.availableStock = Math.max(0, this.currentStock - this.reservedStock);
  
  // Update status based on stock levels
  if (this.availableStock === 0) {
    this.status = PartAvailabilityStatus.OUT_OF_STOCK;
  } else if (this.availableStock <= this.minimumStock) {
    this.status = PartAvailabilityStatus.LOW_STOCK;
  } else {
    this.status = PartAvailabilityStatus.AVAILABLE;
  }
  
  next();
});

export const InventoryPart = vendorDB.model<IInventoryPart>('InventoryPart', InventoryPartSchema);
