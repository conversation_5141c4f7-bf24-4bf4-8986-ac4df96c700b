# Corrective Maintenance Module

This module implements the complete corrective maintenance workflow for the OneCarNow Vendors Platform, as specified in the PRD (Product Requirements Document).

## Overview

The corrective maintenance system allows customers to request maintenance services for vehicle issues not covered by preventive maintenance, including diagnostics, repairs for brakes, tires, suspension, and other mechanical failures.

## Features

### 1. Request and Scheduling
- **Customer-initiated requests**: Customers can request corrective maintenance through the platform
- **Preventive maintenance detection**: Issues detected during preventive maintenance automatically trigger corrective maintenance workflow
- **Vehicle arrival methods**: Support for vehicles arriving under their own power or requiring tow truck
- **Failure classification**: Known failures (brakes, tires, etc.) vs unknown failures requiring diagnosis

### 2. Diagnosis
- Complete vehicle inspection and diagnosis
- Evidence collection (photos and videos)
- Service identification and cost estimation
- Parts requirement analysis with availability tracking

### 3. Quotation System
- Detailed quotations with individual service breakdown
- Cost and time estimates for each service
- Parts cost and labor cost separation
- SLA tracking and compliance monitoring

### 4. Approval Workflow
- Fleet approval for company vehicles
- Customer approval for individual services
- Individual service approval/rejection capability
- Partial approval support

### 5. Service Execution
- Individual service tracking with status updates
- Parts management and availability monitoring
- Progress documentation with photos/videos
- Quality control and warranty tracking

### 6. Notifications
- Email notifications for all workflow stages
- Customer and fleet notifications
- Workshop notifications for new requests
- Progress updates and completion notifications

## API Endpoints

### Orders
- `POST /vendor-platform/corrective-maintenance/orders` - Create new corrective maintenance order
- `GET /vendor-platform/corrective-maintenance/orders` - Get orders with filters
- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get specific order

### Diagnosis
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/diagnosis` - Complete diagnosis

### Quotations
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/quotation` - Create quotation
- `GET /vendor-platform/corrective-maintenance/quotations` - Get quotations
- `POST /vendor-platform/corrective-maintenance/quotations/:quotationId/submit` - Submit for approval

### Approvals
- `POST /vendor-platform/corrective-maintenance/quotations/:quotationId/approve` - Process approval decisions

### Service Execution
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/start` - Start work
- `PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress` - Update service progress
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/complete` - Complete order

### Appointments
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/appointment` - Create appointment for corrective maintenance

## Data Models

### CorrectiveMaintenanceOrder
Main order entity tracking the entire corrective maintenance process.

**Key Fields:**
- `stockId`: Vehicle identifier
- `associateId`: Customer identifier
- `type`: customer-initiated | preventive-detected
- `status`: pending | diagnosed | quoted | approved | in-progress | completed | cancelled
- `failureType`: known | unknown
- `arrivalMethod`: driving | tow-truck
- `totalEstimatedCost`: Total estimated cost
- `slaTarget`: SLA compliance target date

### CorrectiveService
Individual service within a corrective maintenance order.

**Key Fields:**
- `serviceType`: Type of service (brakes, tires, suspension, etc.)
- `status`: not-started | in-progress | completed | waiting-for-parts
- `estimatedCost`: Service cost estimate
- `parts`: Required parts with availability tracking
- `slaTarget`: Service-specific SLA target

### Quotation
Quotation document for customer/fleet approval.

**Key Fields:**
- `quotationNumber`: Unique quotation identifier
- `status`: draft | pending-approval | approved | rejected | partially-approved
- `services`: Array of services with individual approval status
- `validUntil`: Quotation expiration date

## Workflow States

### Order Status Flow
1. **pending** → Initial state when order is created
2. **diagnosed** → After diagnosis is completed
3. **quoted** → After quotation is created
4. **approved** → After services are approved
5. **in-progress** → During service execution
6. **completed** → All services completed
7. **cancelled** → Order cancelled

### Service Status Flow
1. **not-started** → Initial state
2. **in-progress** → Service being performed
3. **waiting-for-parts** → Waiting for parts availability
4. **completed** → Service finished
5. **cancelled** → Service cancelled

## Integration Points

### Vehicle Management
- Updates vehicle's `correctiveMaintenanceHistory`
- Tracks maintenance records and costs
- Links to existing vehicle data

### Workshop System
- Integrates with existing workshop scheduling
- Uses workshop capacity and availability
- Leverages existing service infrastructure

### Appointment System
- Creates appointments linked to corrective maintenance orders
- Supports filtering appointments by maintenance type (preventive/corrective)
- Integrates with existing appointment notification system
- Tracks urgency levels for corrective maintenance

### Notification System
- Extends existing email notification infrastructure
- Uses existing transporter configuration
- Follows established notification patterns

### File Management
- Uses existing S3 integration for evidence storage
- Follows established file upload patterns
- Maintains consistent file organization

## Usage Examples

### Creating a Corrective Maintenance Order
```javascript
POST /vendor-platform/corrective-maintenance/orders
{
  "stockId": "vehicle_id",
  "associateId": "customer_id",
  "workshopId": "workshop_id",
  "type": "customer-initiated",
  "failureType": "known",
  "arrivalMethod": "driving",
  "customerDescription": "Brake noise when stopping",
  "canVehicleDrive": true,
  "needsTowTruck": false,
  "approvalType": "fleet"
}
```

### Completing Diagnosis
```javascript
POST /vendor-platform/corrective-maintenance/orders/:orderId/diagnosis
{
  "diagnosisNotes": "Brake pads worn, rotors need resurfacing",
  "services": [
    {
      "serviceType": "brakes",
      "serviceName": "Brake Pad Replacement",
      "description": "Replace front brake pads and resurface rotors",
      "estimatedCost": 2500,
      "estimatedDuration": 3,
      "laborCost": 1500,
      "parts": [
        {
          "name": "Brake Pads - Front",
          "quantity": 1,
          "unitCost": 800,
          "supplier": "Bosch"
        }
      ]
    }
  ]
}
```

### Processing Approval Decisions
```javascript
POST /vendor-platform/corrective-maintenance/quotations/:quotationId/approve
{
  "decisions": [
    {
      "serviceId": "service_id",
      "isApproved": true
    }
  ]
}
```

### Creating Corrective Maintenance Appointment
```javascript
POST /vendor-platform/corrective-maintenance/orders/:orderId/appointment
{
  "startTime": "2024-01-15T09:00:00.000Z",
  "endTime": "2024-01-15T13:00:00.000Z",
  "serviceTypeId": "SERVICE_TYPE_ID", // Required - must be corrective maintenance type
  "failureDescription": "Brake noise when stopping",
  "urgencyLevel": "high"
}
```

### Getting Corrective Service Types
```javascript
GET /vendor-platform/organizations/:organizationId/service-types?maintenanceType=corrective
```

### Filtering Appointments by Maintenance Type
```javascript
GET /vendor-platform/workshops/:workshopId/appointments?maintenanceType=corrective
GET /vendor-platform/organizations/:organizationId/appointments?maintenanceType=preventive
```

## Configuration

### Environment Variables
- Email configuration uses existing `emailSender` and `emailSenderPassword`
- S3 configuration uses existing `BucketNameEnum.VENDOR_PLATFORM`
- Database uses existing `vendorDB` connection

### SLA Defaults
- Diagnosis: 72 hours
- Service completion: Based on individual service estimates
- Parts availability: Tracked per part with ETA

## Monitoring and Metrics

The system tracks key metrics as specified in the PRD:
- % of repairs completed within SLA
- Average repair time by service type
- Total vehicle downtime
- % of approvals completed within 24 hours
- % of quotations rejected
- Customer satisfaction (NPS) post-service

## Testing

Comprehensive testing should cover:
- Order creation and workflow progression
- Diagnosis completion with file uploads
- Quotation generation and approval workflows
- Service execution and progress tracking
- Notification delivery
- SLA compliance tracking
- Parts management and availability

## Future Enhancements

Potential improvements based on usage:
- Mobile app integration for real-time updates
- Integration with parts suppliers for automated ordering
- Advanced analytics and reporting dashboard
- Customer self-service portal for approvals
- Integration with vehicle telematics for proactive maintenance
