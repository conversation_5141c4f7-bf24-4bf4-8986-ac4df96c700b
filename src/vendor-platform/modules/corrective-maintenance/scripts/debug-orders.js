/**
 * Script para debuggear por qué getOrders regresa un arreglo vacío
 */

const mongoose = require('mongoose');

// Conectar a la base de datos
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/onecar-vendor-platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Esquema simplificado para debug
const CorrectiveMaintenanceOrderSchema = new mongoose.Schema({
  organizationId: String,
  workshopId: String,
  stockId: String,
  associateId: String,
  status: String,
  type: String,
  customerDescription: String,
  createdAt: { type: Date, default: Date.now },
}, { collection: 'correctivemaintenanceorders' });

const CorrectiveMaintenanceOrder = mongoose.model('CorrectiveMaintenanceOrder', CorrectiveMaintenanceOrderSchema);

const debugOrders = async () => {
  await connectDB();

  console.log('🔍 Debugging Corrective Maintenance Orders');
  console.log('==========================================');

  try {
    // 1. Contar total de documentos
    const totalCount = await CorrectiveMaintenanceOrder.countDocuments();
    console.log(`📊 Total orders in database: ${totalCount}`);

    if (totalCount === 0) {
      console.log('❌ No orders found in database!');
      console.log('💡 This explains why getOrders returns empty array');
      return;
    }

    // 2. Obtener todas las órdenes sin filtros
    const allOrders = await CorrectiveMaintenanceOrder.find().limit(5);
    console.log(`\n📋 Sample orders (first 5):`);
    allOrders.forEach((order, index) => {
      console.log(`${index + 1}. ID: ${order._id}`);
      console.log(`   Organization: ${order.organizationId}`);
      console.log(`   Workshop: ${order.workshopId}`);
      console.log(`   Stock: ${order.stockId}`);
      console.log(`   Associate: ${order.associateId}`);
      console.log(`   Status: ${order.status}`);
      console.log(`   Type: ${order.type}`);
      console.log(`   Created: ${order.createdAt}`);
      console.log('   ---');
    });

    // 3. Verificar organizaciones únicas
    const organizations = await CorrectiveMaintenanceOrder.distinct('organizationId');
    console.log(`\n🏢 Unique organizations: ${organizations.length}`);
    organizations.forEach(org => console.log(`   - ${org}`));

    // 4. Verificar workshops únicos
    const workshops = await CorrectiveMaintenanceOrder.distinct('workshopId');
    console.log(`\n🔧 Unique workshops: ${workshops.length}`);
    workshops.forEach(workshop => console.log(`   - ${workshop}`));

    // 5. Verificar status únicos
    const statuses = await CorrectiveMaintenanceOrder.distinct('status');
    console.log(`\n📊 Unique statuses: ${statuses.length}`);
    statuses.forEach(status => console.log(`   - ${status}`));

    // 6. Verificar types únicos
    const types = await CorrectiveMaintenanceOrder.distinct('type');
    console.log(`\n🔧 Unique types: ${types.length}`);
    types.forEach(type => console.log(`   - ${type}`));

    // 7. Simular query con filtros comunes
    console.log(`\n🧪 Testing common filter scenarios:`);
    
    // Test con organizationId más común
    if (organizations.length > 0) {
      const mostCommonOrg = organizations[0];
      const orgOrders = await CorrectiveMaintenanceOrder.find({ organizationId: mostCommonOrg });
      console.log(`   📊 Orders for org ${mostCommonOrg}: ${orgOrders.length}`);
    }

    // Test sin filtros de organización
    const noOrgFilter = await CorrectiveMaintenanceOrder.find({});
    console.log(`   📊 Orders without org filter: ${noOrgFilter.length}`);

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
};

// Ejecutar debug
debugOrders().catch(console.error);
