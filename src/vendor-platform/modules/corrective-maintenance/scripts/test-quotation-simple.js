/**
 * Simple test to verify quotation number generation logic
 */

// Mock the quotation number generation logic
function generateQuotationNumber(count = 0) {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const sequentialNumber = String(count + 1).padStart(6, '0');
  
  return `COT-${year}${month}-${sequentialNumber}`;
}

console.log('🧪 Testing Quotation Number Generation Logic');
console.log('=============================================');

// Test the generation logic
console.log('\n📋 Testing quotation number format:');
for (let i = 0; i < 5; i++) {
  const quotationNumber = generateQuotationNumber(i);
  console.log(`Count ${i}: ${quotationNumber}`);
}

// Test format validation
const formatRegex = /^COT-\d{6}-\d{6}$/;
const testNumber = generateQuotationNumber(0);
console.log(`\n✅ Format validation: ${formatRegex.test(testNumber) ? 'PASS' : 'FAIL'}`);
console.log(`Generated: ${testNumber}`);
console.log(`Expected format: COT-YYYYMM-XXXXXX`);

console.log('\n🎉 Logic test completed!');
