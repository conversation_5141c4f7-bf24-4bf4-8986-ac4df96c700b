/**
 * Test script to test the completeOrder endpoint
 */

const axios = require('axios');
const FormData = require('form-data');

async function testCompleteOrder() {
  try {
    console.log('🧪 Testing Complete Order Endpoint');
    console.log('==================================');

    const baseURL = 'http://localhost:3000';
    const orderId = '683cf8b7545e6ca21d70276a'; // From the original error
    
    console.log('📡 Making API request...');
    console.log(`URL: ${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/complete`);

    // Test 1: Basic completion
    console.log('\n1️⃣ Testing basic order completion...');
    
    const basicCompletionData = {
      completionNotes: 'All corrective maintenance services completed successfully. Vehicle tested and ready for delivery.',
      finalInspectionPassed: true,
      workQualityRating: 5
    };

    const response1 = await axios.post(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/complete`,
      basicCompletionData,
      {
        headers: {
          'Content-Type': 'application/json',
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ Basic completion response:', response1.status);
    if (response1.data.data) {
      console.log('📋 Order Status:', response1.data.data.status);
      console.log('📋 Completion Date:', response1.data.data.completionDate);
      console.log('📋 Total Duration:', response1.data.data.totalDurationHours, 'hours');
      console.log('📋 SLA Compliance:', response1.data.data.slaCompliance);
      console.log('📋 Total Actual Cost:', response1.data.data.totalActualCost);
      console.log('📋 Work Quality Rating:', response1.data.data.workQualityRating);
    }

  } catch (error) {
    console.error('❌ API call failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.error('🔍 AUTHENTICATION ERROR: Need proper auth token');
      } else if (error.response.status === 404) {
        console.error('🔍 NOT FOUND: Order might not exist or no access');
      } else if (error.response.status === 400) {
        console.error('🔍 BAD REQUEST: Check order status and requirements');
        if (error.response.data.message) {
          console.error('Error message:', error.response.data.message);
          
          // Check for specific error types
          if (error.response.data.message.includes('already completed')) {
            console.error('💡 Order is already completed');
          } else if (error.response.data.message.includes('pending completion')) {
            console.error('💡 Some services are still not completed');
          } else if (error.response.data.message.includes('rating must be between')) {
            console.error('💡 Invalid rating provided');
          }
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

async function testWithFormData() {
  try {
    console.log('\n2️⃣ Testing with form data and photos...');
    
    const baseURL = 'http://localhost:3000';
    const orderId = '683cf8b7545e6ca21d70276a';
    
    const formData = new FormData();
    formData.append('completionNotes', 'Comprehensive corrective maintenance completed with excellent results');
    formData.append('finalInspectionPassed', 'true');
    formData.append('customerSatisfactionRating', '5');
    formData.append('workQualityRating', '5');
    formData.append('totalActualCost', '12750');
    
    // Add recommendations
    const recommendations = JSON.stringify([
      'Schedule brake inspection in 6 months',
      'Monitor tire wear patterns closely',
      'Check engine oil every 3,000 km'
    ]);
    formData.append('recommendationsForFuture', recommendations);

    const response = await axios.post(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/complete`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ Form data completion response:', response.status);
    if (response.data.data) {
      console.log('📋 Order Status:', response.data.data.status);
      console.log('📋 Customer Satisfaction:', response.data.data.customerSatisfactionRating);
      console.log('📋 Work Quality Rating:', response.data.data.workQualityRating);
      console.log('📋 Total Actual Cost:', response.data.data.totalActualCost);
      console.log('📋 Recommendations Count:', response.data.data.recommendationsForFuture?.length || 0);
      console.log('📋 After Photos Count:', response.data.data.afterPhotos?.length || 0);
    }

  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️  Authentication required - cannot test form data');
    } else {
      console.error('Error in form data test:', error.message);
    }
  }
}

async function testCompletionFlow() {
  console.log('\n🔄 Testing Complete Order Flow');
  console.log('==============================');
  
  const baseURL = 'http://localhost:3000';
  const orderId = '683cf8b7545e6ca21d70276a';
  
  try {
    // Step 1: Check order status before completion
    console.log('\n1️⃣ Checking order status before completion...');
    const orderResponse = await axios.get(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      {
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    if (orderResponse.data.data) {
      console.log('📋 Current order status:', orderResponse.data.data.status);
      console.log('📋 Start date:', orderResponse.data.data.startDate);
      
      if (orderResponse.data.data.services) {
        const services = orderResponse.data.data.services;
        const completedServices = services.filter(s => s.status === 'completed');
        const inProgressServices = services.filter(s => s.status === 'in-progress');
        const waitingServices = services.filter(s => s.status === 'waiting-for-parts');
        
        console.log('📋 Services status:');
        console.log(`   - Completed: ${completedServices.length}`);
        console.log(`   - In Progress: ${inProgressServices.length}`);
        console.log(`   - Waiting for Parts: ${waitingServices.length}`);
        console.log(`   - Total: ${services.length}`);
        
        if (completedServices.length === services.length) {
          console.log('✅ All services completed - ready for order completion');
        } else {
          console.log('⚠️  Not all services completed - order completion may fail');
        }
      }
    }
    
    // Step 2: Try to complete the order
    console.log('\n2️⃣ Attempting to complete order...');
    await testCompleteOrder();
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️  Authentication required - cannot test full flow');
      console.log('💡 Try testing with proper authentication tokens');
    } else {
      console.error('Error in completion flow test:', error.message);
    }
  }
}

async function testValidationScenarios() {
  console.log('\n3️⃣ Testing validation scenarios...');
  
  const baseURL = 'http://localhost:3000';
  const orderId = '683cf8b7545e6ca21d70276a';
  
  const testCases = [
    {
      name: 'Invalid Customer Rating (too high)',
      data: {
        completionNotes: 'Test completion',
        customerSatisfactionRating: 6 // Invalid - should be 1-5
      }
    },
    {
      name: 'Invalid Work Quality Rating (too low)',
      data: {
        completionNotes: 'Test completion',
        workQualityRating: 0 // Invalid - should be 1-5
      }
    },
    {
      name: 'Valid ratings',
      data: {
        completionNotes: 'Test completion with valid ratings',
        customerSatisfactionRating: 4,
        workQualityRating: 5
      }
    }
  ];

  for (let i = 0; i < testCases.length; i++) {
    const { name, data } = testCases[i];
    
    try {
      console.log(`\n   ${i + 1}. ${name}...`);
      
      const response = await axios.post(
        `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/complete`,
        data,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      );
      
      console.log(`   ✅ ${name} - Response:`, response.status);
      
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`   ✅ ${name} - Validation error (expected):`, error.response.data.message);
      } else if (error.response?.status === 401) {
        console.log(`   ⚠️  ${name} - Authentication required`);
        break;
      } else {
        console.error(`   ❌ ${name} - Unexpected error:`, error.response?.data?.message || error.message);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 300));
  }
}

// Run the tests
console.log('🚀 Starting Complete Order Tests');
console.log('================================\n');

async function runAllTests() {
  await testCompletionFlow();
  await testWithFormData();
  await testValidationScenarios();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n💡 Notes:');
  console.log('- Replace orderId with actual order ID');
  console.log('- Add proper authentication tokens');
  console.log('- Ensure all services are completed before testing');
  console.log('- Test with actual image files for photo uploads');
}

runAllTests();
