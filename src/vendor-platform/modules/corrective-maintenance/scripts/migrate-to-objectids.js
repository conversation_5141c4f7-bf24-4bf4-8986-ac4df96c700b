/**
 * Migration script to convert stockId and associateId from strings to ObjectIds
 * in existing corrective maintenance orders
 */

const mongoose = require('mongoose');

// Connect to the database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/onecar-vendor-platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Migration function
const migrateToObjectIds = async () => {
  try {
    await connectDB();

    // Use the vendor_platform database
    const vendorDB = mongoose.connection.useDb('vendor_platform', { useCache: true });
    const CorrectiveMaintenanceOrder = vendorDB.collection('correctivemaintenanceorders');

    console.log('\n🔄 Starting migration to ObjectIds...');

    // Find all documents where stockId or associateId are strings
    const documentsToMigrate = await CorrectiveMaintenanceOrder.find({
      $or: [
        { stockId: { $type: 'string' } },
        { associateId: { $type: 'string' } }
      ]
    }).toArray();

    console.log(`📊 Found ${documentsToMigrate.length} documents to migrate`);

    if (documentsToMigrate.length === 0) {
      console.log('✅ No documents need migration');
      process.exit(0);
    }

    let migratedCount = 0;
    let errorCount = 0;

    for (const doc of documentsToMigrate) {
      try {
        const updateFields = {};

        // Convert stockId if it's a string
        if (typeof doc.stockId === 'string') {
          if (mongoose.Types.ObjectId.isValid(doc.stockId)) {
            updateFields.stockId = new mongoose.Types.ObjectId(doc.stockId);
            console.log(`  Converting stockId: ${doc.stockId} -> ObjectId`);
          } else {
            console.warn(`  ⚠️  Invalid stockId format: ${doc.stockId}`);
          }
        }

        // Convert associateId if it's a string
        if (typeof doc.associateId === 'string') {
          if (mongoose.Types.ObjectId.isValid(doc.associateId)) {
            updateFields.associateId = new mongoose.Types.ObjectId(doc.associateId);
            console.log(`  Converting associateId: ${doc.associateId} -> ObjectId`);
          } else {
            console.warn(`  ⚠️  Invalid associateId format: ${doc.associateId}`);
          }
        }

        // Update the document if there are fields to update
        if (Object.keys(updateFields).length > 0) {
          await CorrectiveMaintenanceOrder.updateOne(
            { _id: doc._id },
            { $set: updateFields }
          );
          migratedCount++;
          console.log(`  ✅ Migrated document ${doc._id}`);
        }

      } catch (error) {
        console.error(`  ❌ Error migrating document ${doc._id}:`, error.message);
        errorCount++;
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`  Total documents found: ${documentsToMigrate.length}`);
    console.log(`  Successfully migrated: ${migratedCount}`);
    console.log(`  Errors: ${errorCount}`);

    // Verify migration
    console.log('\n🔍 Verifying migration...');
    const remainingStringIds = await CorrectiveMaintenanceOrder.find({
      $or: [
        { stockId: { $type: 'string' } },
        { associateId: { $type: 'string' } }
      ]
    }).toArray();

    if (remainingStringIds.length === 0) {
      console.log('✅ Migration completed successfully! All IDs are now ObjectIds.');
    } else {
      console.log(`⚠️  ${remainingStringIds.length} documents still have string IDs`);
    }

    console.log('\n🎉 Migration process completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
};

// Run the migration
migrateToObjectIds();
