/**
 * Test script to test the updateServiceProgress endpoint
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testUpdateServiceProgress() {
  try {
    console.log('🧪 Testing Update Service Progress Endpoint');
    console.log('==========================================');

    const baseURL = 'http://localhost:3000';
    const serviceId = 'service_id_123'; // Replace with actual service ID
    
    console.log('📡 Making API request...');
    console.log(`URL: ${baseURL}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`);

    // Test 1: Basic status update
    console.log('\n1️⃣ Testing basic status update...');
    
    const basicUpdateData = {
      status: 'in-progress',
      notes: 'Started working on brake service',
      actualCost: 2500
    };

    const response1 = await axios.patch(
      `${baseURL}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
      basicUpdateData,
      {
        headers: {
          'Content-Type': 'application/json',
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ Basic update response:', response1.status);
    if (response1.data.data) {
      console.log('📋 Service Status:', response1.data.data.status);
      console.log('📋 Actual Cost:', response1.data.data.actualCost);
      console.log('📋 Notes Count:', response1.data.data.technicalNotes?.length || 0);
    }

  } catch (error) {
    console.error('❌ API call failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.error('🔍 AUTHENTICATION ERROR: Need proper auth token');
      } else if (error.response.status === 404) {
        console.error('🔍 NOT FOUND: Service might not exist or no access');
      } else if (error.response.status === 400) {
        console.error('🔍 BAD REQUEST: Check service status and requirements');
        if (error.response.data.message) {
          console.error('Error message:', error.response.data.message);
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

async function testWithFormData() {
  try {
    console.log('\n2️⃣ Testing with form data and parts...');
    
    const baseURL = 'http://localhost:3000';
    const serviceId = 'service_id_123';
    
    const formData = new FormData();
    formData.append('status', 'completed');
    formData.append('notes', 'Brake service completed successfully');
    formData.append('actualCost', '2800');
    formData.append('qualityCheckPassed', 'true');
    
    // Add parts data
    const partsData = JSON.stringify([
      {
        name: 'Brake Pads',
        quantity: 4,
        unitCost: 150.00,
        supplier: 'AutoParts Inc'
      },
      {
        name: 'Brake Fluid',
        quantity: 1,
        unitCost: 25.00,
        supplier: 'AutoParts Inc'
      }
    ]);
    formData.append('partsUsed', partsData);
    
    // Add technical notes
    const technicalNotes = JSON.stringify([
      'Replaced all brake pads',
      'Checked brake fluid level',
      'Tested brake performance'
    ]);
    formData.append('technicalNotes', technicalNotes);

    const response = await axios.patch(
      `${baseURL}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ Form data update response:', response.status);
    if (response.data.data) {
      console.log('📋 Service Status:', response.data.data.status);
      console.log('📋 Quality Check:', response.data.data.qualityCheckPassed);
      console.log('📋 Total Parts Cost:', response.data.data.totalPartsCost);
      console.log('📋 Parts Count:', response.data.data.parts?.length || 0);
      console.log('📋 Technical Notes:', response.data.data.technicalNotes?.length || 0);
    }

  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️  Authentication required - cannot test form data');
    } else {
      console.error('Error in form data test:', error.message);
    }
  }
}

async function testProgressFlow() {
  console.log('\n🔄 Testing Complete Progress Flow');
  console.log('=================================');
  
  const baseURL = 'http://localhost:3000';
  const serviceId = 'service_id_123';
  
  const progressSteps = [
    {
      step: 'Start Service',
      data: {
        status: 'in-progress',
        notes: 'Started brake service inspection'
      }
    },
    {
      step: 'Add Progress',
      data: {
        notes: 'Removed old brake pads',
        progressPhotos: ['photo1_url', 'photo2_url']
      }
    },
    {
      step: 'Update Parts',
      data: {
        notes: 'Installing new brake pads',
        partsUsed: JSON.stringify([
          {
            name: 'Brake Pads',
            quantity: 4,
            unitCost: 150.00
          }
        ])
      }
    },
    {
      step: 'Complete Service',
      data: {
        status: 'completed',
        notes: 'Brake service completed and tested',
        actualCost: 2750,
        qualityCheckPassed: true
      }
    }
  ];

  for (let i = 0; i < progressSteps.length; i++) {
    const { step, data } = progressSteps[i];
    
    try {
      console.log(`\n${i + 1}️⃣ ${step}...`);
      
      const response = await axios.patch(
        `${baseURL}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
        data,
        {
          headers: { 'Content-Type': 'application/json' }
        }
      );
      
      console.log(`✅ ${step} successful:`, response.status);
      if (response.data.data) {
        console.log('   Status:', response.data.data.status);
        console.log('   Notes count:', response.data.data.technicalNotes?.length || 0);
      }
      
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`⚠️  Authentication required for ${step}`);
        break;
      } else {
        console.error(`❌ ${step} failed:`, error.response?.data?.message || error.message);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

// Run the tests
console.log('🚀 Starting Update Service Progress Tests');
console.log('=========================================\n');

async function runAllTests() {
  await testUpdateServiceProgress();
  await testWithFormData();
  await testProgressFlow();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n💡 Notes:');
  console.log('- Replace serviceId with actual service ID');
  console.log('- Add proper authentication tokens');
  console.log('- Test with actual image files for photo uploads');
}

runAllTests();
