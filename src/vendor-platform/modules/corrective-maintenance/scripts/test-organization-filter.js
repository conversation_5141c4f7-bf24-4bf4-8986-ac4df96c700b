/**
 * <PERSON><PERSON><PERSON> to test the organizationId filtering fix
 */

const mongoose = require('mongoose');

// Connect to the database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/onecar-vendor-platform');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Test the filtering
const testOrganizationFilter = async () => {
  try {
    await connectDB();

    // Use the vendor_platform database
    const vendorDB = mongoose.connection.useDb('vendor_platform', { useCache: true });

    // Get the collection directly
    const CorrectiveMaintenanceOrder = vendorDB.collection('correctivemaintenanceorders');

    // Test organizationId from your example
    const testOrgId = '6747ba9281bc271fbd266024';
    const testStockId = '680a4566c19a70c30eed8994';
    const testAssociateId = '680a4a67c19a70c30eed9c8d';

    console.log('\n🔍 Testing ObjectId filtering...');
    console.log('Target organizationId:', testOrgId);
    console.log('Target stockId:', testStockId);
    console.log('Target associateId:', testAssociateId);

    // Test 1: Query with string (old way - should fail)
    console.log('\n1️⃣ Testing with string filters (old way):');
    const stringResult = await CorrectiveMaintenanceOrder.find({
      organizationId: testOrgId,
      stockId: testStockId,
      associateId: testAssociateId
    }).toArray();
    console.log('Results with string filters:', stringResult.length);

    // Test 2: Query with ObjectId (new way - should work)
    console.log('\n2️⃣ Testing with ObjectId filters (new way):');
    const objectIdResult = await CorrectiveMaintenanceOrder.find({
      organizationId: new mongoose.Types.ObjectId(testOrgId),
      stockId: new mongoose.Types.ObjectId(testStockId),
      associateId: new mongoose.Types.ObjectId(testAssociateId)
    }).toArray();
    console.log('Results with ObjectId filters:', objectIdResult.length);

    // Test 3: Show sample documents
    console.log('\n3️⃣ Sample documents in collection:');
    const sampleDocs = await CorrectiveMaintenanceOrder.find({}).limit(3).toArray();
    sampleDocs.forEach((doc, index) => {
      console.log(`Document ${index + 1}:`);
      console.log('  _id:', doc._id);
      console.log('  organizationId:', doc.organizationId);
      console.log('  stockId:', doc.stockId);
      console.log('  associateId:', doc.associateId);
      console.log('  organizationId type:', typeof doc.organizationId);
      console.log('  stockId type:', typeof doc.stockId);
      console.log('  associateId type:', typeof doc.associateId);
      console.log('  organizationId string:', doc.organizationId.toString());
      console.log('  stockId string:', doc.stockId.toString());
      console.log('  associateId string:', doc.associateId.toString());
      console.log('  matches target org:', doc.organizationId.toString() === testOrgId);
      console.log('  matches target stock:', doc.stockId.toString() === testStockId);
      console.log('  matches target associate:', doc.associateId.toString() === testAssociateId);
      console.log('');
    });

    console.log('✅ Test completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
};

// Run the test
testOrganizationFilter();
