/**
 * Test script to call the quotation API endpoint
 */

const axios = require('axios');

async function testQuotationAPI() {
  try {
    console.log('🧪 Testing Quotation API Endpoint');
    console.log('==================================');

    const baseURL = 'http://localhost:3000';
    const orderId = '683cf8b7545e6ca21d70276a'; // From the original error
    
    const requestData = {
      approvalType: 'fleet',
      validityDays: 15,
      customerNotes: '',
      paymentTerms: 'Pago contra entrega',
      services: [
        {
          serviceId: '683d1d586de66c580053d6e2',
          serviceName: 'Frenos',
          description: 'Cambio de balatas delanteras',
          estimatedCost: 2000,
          laborCost: 500,
          estimatedDuration: 48
        },
        {
          serviceId: '683d1d586de66c580053d6e4',
          serviceName: 'Llantas',
          description: 'Cambio de las 4 llantas',
          estimatedCost: 10000,
          laborCost: 100,
          estimatedDuration: 72
        }
      ]
    };

    console.log('📡 Making API request...');
    console.log(`URL: ${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/quotation`);
    console.log('Data:', JSON.stringify(requestData, null, 2));

    const response = await axios.post(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/quotation`,
      requestData,
      {
        headers: {
          'Content-Type': 'application/json',
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ SUCCESS! Response:', response.status);
    console.log('📄 Response data:', JSON.stringify(response.data, null, 2));

    // Check if quotationNumber was generated
    if (response.data.data && response.data.data.quotationNumber) {
      console.log('🎉 Quotation number generated:', response.data.data.quotationNumber);
    } else {
      console.log('❌ No quotation number found in response');
    }

  } catch (error) {
    console.error('❌ API call failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      
      // Check if it's the same validation error
      if (error.response.data.error && error.response.data.error.message) {
        if (error.response.data.error.message.includes('quotationNumber')) {
          console.error('🔍 SAME ERROR: quotationNumber validation failed - fix not working');
        } else {
          console.error('🔍 DIFFERENT ERROR: Not the quotationNumber issue');
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Run the test
testQuotationAPI();
