/**
 * <PERSON><PERSON><PERSON> to setup corrective maintenance service types
 * Run this script to create default corrective maintenance service types for organizations
 */

import mongoose from 'mongoose';
import { ServiceTypeVendorModel } from '../../serviceType/models/serviceType.model';
import OrganizationModel from '../../organizations/models/organization.model';

// Default corrective maintenance service types
const defaultCorrectiveServiceTypes = [
  {
    name: 'Diagnóstico General',
    description: 'Diagnóstico completo del vehículo para identificar fallas',
    duration: 120, // 2 hours
    maintenanceType: 'corrective' as const,
    price: 500,
    includedServices: ['diagnostic', 'inspection', 'report'],
  },
  {
    name: 'Reparación de Frenos',
    description: 'Cambio de pastillas, discos y reparación del sistema de frenos',
    duration: 180, // 3 hours
    maintenanceType: 'corrective' as const,
    price: 2500,
    includedServices: ['brake-pads', 'brake-discs', 'brake-fluid', 'inspection'],
  },
  {
    name: 'Cambio de Llantas',
    description: 'Cambio de llantas y balanceeo',
    duration: 90, // 1.5 hours
    maintenanceType: 'corrective' as const,
    price: 3000,
    includedServices: ['tire-replacement', 'wheel-balancing', 'alignment-check'],
  },
  {
    name: 'Reparación de Suspensión',
    description: 'Reparación y cambio de componentes de suspensión',
    duration: 240, // 4 hours
    maintenanceType: 'corrective' as const,
    price: 4000,
    includedServices: ['shock-absorbers', 'springs', 'bushings', 'alignment'],
  },
  {
    name: 'Reparación de Motor',
    description: 'Reparaciones generales del motor',
    duration: 480, // 8 hours
    maintenanceType: 'corrective' as const,
    price: 8000,
    includedServices: ['engine-repair', 'parts-replacement', 'testing'],
  },
  {
    name: 'Reparación de Transmisión',
    description: 'Reparación y mantenimiento de la transmisión',
    duration: 360, // 6 hours
    maintenanceType: 'corrective' as const,
    price: 6000,
    includedServices: ['transmission-repair', 'fluid-change', 'testing'],
  },
  {
    name: 'Sistema Eléctrico',
    description: 'Diagnóstico y reparación del sistema eléctrico',
    duration: 180, // 3 hours
    maintenanceType: 'corrective' as const,
    price: 2000,
    includedServices: ['electrical-diagnosis', 'wiring-repair', 'component-replacement'],
  },
  {
    name: 'Carrocería y Pintura',
    description: 'Reparación de carrocería y trabajos de pintura',
    duration: 720, // 12 hours (puede ser varios días)
    maintenanceType: 'corrective' as const,
    price: 10000,
    includedServices: ['bodywork', 'painting', 'dent-repair'],
  },
  {
    name: 'Sistema de Enfriamiento',
    description: 'Reparación del sistema de enfriamiento del motor',
    duration: 150, // 2.5 hours
    maintenanceType: 'corrective' as const,
    price: 1500,
    includedServices: ['radiator-repair', 'coolant-change', 'thermostat', 'hoses'],
  },
  {
    name: 'Sistema de Escape',
    description: 'Reparación del sistema de escape',
    duration: 120, // 2 hours
    maintenanceType: 'corrective' as const,
    price: 1800,
    includedServices: ['exhaust-repair', 'muffler', 'catalytic-converter'],
  },
];

export async function setupCorrectiveServiceTypes(organizationId?: string) {
  try {
    console.log('🔧 Setting up corrective maintenance service types...');

    let organizations;
    if (organizationId) {
      organizations = await OrganizationModel.find({ _id: organizationId });
    } else {
      organizations = await OrganizationModel.find({});
    }

    if (organizations.length === 0) {
      console.log('❌ No organizations found');
      return;
    }

    for (const organization of organizations) {
      console.log(`\n📋 Processing organization: ${organization.name} (${organization._id})`);

      for (const serviceTypeData of defaultCorrectiveServiceTypes) {
        // Check if service type already exists
        const existingServiceType = await ServiceTypeVendorModel.findOne({
          name: serviceTypeData.name,
          organization: organization._id,
          maintenanceType: 'corrective',
        });

        if (existingServiceType) {
          console.log(`   ⚠️  Service type "${serviceTypeData.name}" already exists, skipping...`);
          continue;
        }

        // Create new service type
        const newServiceType = new ServiceTypeVendorModel({
          ...serviceTypeData,
          organization: organization._id,
          isActive: true,
        });

        await newServiceType.save();
        console.log(`   ✅ Created service type: "${serviceTypeData.name}"`);
      }
    }

    console.log('\n🎉 Corrective maintenance service types setup completed!');
  } catch (error) {
    console.error('❌ Error setting up corrective service types:', error);
    throw error;
  }
}

// Function to run the script directly
export async function runSetupScript() {
  try {
    // Connect to database if not already connected
    if (mongoose.connection.readyState === 0) {
      console.log('🔌 Connecting to database...');
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/onecarnow');
    }

    await setupCorrectiveServiceTypes();

    console.log('✅ Script completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run script if called directly
if (require.main === module) {
  runSetupScript();
}
