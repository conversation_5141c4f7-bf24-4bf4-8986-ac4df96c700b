/**
 * Script para probar el mapeo de service types a CorrectiveServiceType
 */

const CorrectiveServiceType = {
  BRAKES: 'brakes',
  TIRES: 'tires',
  SUSPENSION: 'suspension',
  ENGINE: 'engine',
  TRANSMISSION: 'transmission',
  ELECTRICAL: 'electrical',
  BODYWORK: 'bodywork',
  CLUTCH: 'clutch',
  COOLING_SYSTEM: 'cooling-system',
  EXHAUST: 'exhaust',
  FUEL_SYSTEM: 'fuel-system',
  STEERING: 'steering',
  OTHER: 'other',
};

const mapServiceTypeToCorrectiveServiceType = (serviceTypeName) => {
  const name = serviceTypeName.toLowerCase();
  
  if (name.includes('freno') || name.includes('brake')) return CorrectiveServiceType.BRAKES;
  if (name.includes('llanta') || name.includes('tire') || name.includes('neumático')) return CorrectiveServiceType.TIRES;
  if (name.includes('suspensión') || name.includes('suspension')) return CorrectiveServiceType.SUSPENSION;
  if (name.includes('motor') || name.includes('engine')) return CorrectiveServiceType.ENGINE;
  if (name.includes('transmisión') || name.includes('transmission')) return CorrectiveServiceType.TRANSMISSION;
  if (name.includes('eléctrico') || name.includes('electrical')) return CorrectiveServiceType.ELECTRICAL;
  if (name.includes('carrocería') || name.includes('bodywork') || name.includes('pintura')) return CorrectiveServiceType.BODYWORK;
  if (name.includes('embrague') || name.includes('clutch')) return CorrectiveServiceType.CLUTCH;
  if (name.includes('enfriamiento') || name.includes('cooling')) return CorrectiveServiceType.COOLING_SYSTEM;
  if (name.includes('escape') || name.includes('exhaust')) return CorrectiveServiceType.EXHAUST;
  if (name.includes('combustible') || name.includes('fuel')) return CorrectiveServiceType.FUEL_SYSTEM;
  if (name.includes('dirección') || name.includes('steering')) return CorrectiveServiceType.STEERING;
  
  return CorrectiveServiceType.OTHER;
};

const testCases = [
  'Diagnóstico General',
  'Reparación de Frenos',
  'Cambio de Llantas',
  'Reparación de Suspensión',
  'Reparación de Motor',
  'Reparación de Transmisión',
  'Sistema Eléctrico',
  'Carrocería y Pintura',
  'Sistema de Enfriamiento',
  'Sistema de Escape',
  'Brake Repair',
  'Engine Diagnostic',
  'Tire Change',
  'Unknown Service',
];

console.log('🧪 Testing Service Type Mapping');
console.log('================================');

testCases.forEach(serviceTypeName => {
  const mapped = mapServiceTypeToCorrectiveServiceType(serviceTypeName);
  console.log(`"${serviceTypeName}" → ${mapped}`);
});

console.log('\n✅ Mapping test completed!');
console.log('\n📋 Available CorrectiveServiceType values:');
Object.values(CorrectiveServiceType).forEach(value => {
  console.log(`  - ${value}`);
});

// Test específico para el error que estamos viendo
console.log('\n🔍 Testing problematic cases:');
console.log(`"repair" → ${mapServiceTypeToCorrectiveServiceType('repair')}`);
console.log(`"diagnostic" → ${mapServiceTypeToCorrectiveServiceType('diagnostic')}`);
