/**
 * Simple script to test the quotation number generation fix
 */

import { Types } from 'mongoose';
import { Quotation, QuotationStatus } from '../models/quotation.model';
import vendorDB from '@/vendor-platform/db';

async function testQuotationNumberGeneration() {
  try {
    console.log('🧪 Testing Quotation Number Generation Fix');
    console.log('==========================================');

    // Connect to database
    console.log('📡 Connecting to database...');
    if (vendorDB.readyState !== 1) {
      await vendorDB.asPromise();
    }
    console.log('✅ Database connected');

    // Test data
    const mockQuotationData = {
      orderId: new Types.ObjectId(),
      status: QuotationStatus.DRAFT,
      version: 1,
      services: [
        {
          serviceId: new Types.ObjectId(),
          serviceName: 'Test Service',
          description: 'Test service description',
          estimatedCost: 1000,
          estimatedDuration: 24,
          laborCost: 500,
          partsCost: 500,
          isApproved: false,
          parts: [],
          slaEstimate: 24,
          partsAvailabilityDelay: 0,
        },
      ],
      totalEstimatedCost: 1000,
      totalEstimatedDuration: 24,
      totalLaborCost: 500,
      totalPartsCost: 500,
      approvalRequired: true,
      approvalType: 'fleet' as const,
      approvedServices: [],
      rejectedServices: [],
      validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
      overallSLA: 24,
      earliestStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      estimatedCompletionDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
      diagnosticEvidence: {
        photos: [],
        videos: [],
        reports: [],
      },
    };

    console.log('\n🔧 Creating quotation without quotationNumber...');

    // Create quotation without quotationNumber
    const quotation = new Quotation(mockQuotationData);

    console.log('📋 Before save - quotationNumber:', quotation.quotationNumber || 'undefined');

    // Save the quotation (this should trigger the pre-save middleware)
    await quotation.save();

    console.log('📋 After save - quotationNumber:', quotation.quotationNumber);

    // Verify the quotation number was generated
    if (quotation.quotationNumber) {
      console.log('✅ SUCCESS: Quotation number was generated!');
      console.log('📄 Generated format:', quotation.quotationNumber);

      // Check if it matches the expected format: COT-YYYYMM-XXXXXX
      const formatRegex = /^COT-\d{6}-\d{6}$/;
      if (formatRegex.test(quotation.quotationNumber)) {
        console.log('✅ SUCCESS: Quotation number format is correct!');
      } else {
        console.log('❌ ERROR: Quotation number format is incorrect!');
        console.log('Expected format: COT-YYYYMM-XXXXXX');
        console.log('Actual format:', quotation.quotationNumber);
      }
    } else {
      console.log('❌ ERROR: Quotation number was not generated!');
    }

    // Test creating another quotation to ensure uniqueness
    console.log('\n🔧 Creating second quotation to test uniqueness...');
    const quotation2 = new Quotation({
      ...mockQuotationData,
      orderId: new Types.ObjectId(),
    });

    await quotation2.save();

    console.log('📄 Second quotation number:', quotation2.quotationNumber);

    if (quotation.quotationNumber !== quotation2.quotationNumber) {
      console.log('✅ SUCCESS: Quotation numbers are unique!');
    } else {
      console.log('❌ ERROR: Quotation numbers are not unique!');
    }

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await Quotation.deleteMany({
      _id: { $in: [quotation._id, quotation2._id] },
    });
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Test completed successfully!');
  } catch (error) {
    console.error('❌ Test failed with error:', error);

    if (error instanceof Error && error.name === 'ValidationError') {
      console.error('📋 Validation errors:', (error as any).errors);
    }
  } finally {
    // Close database connection
    await vendorDB.close();
    console.log('📡 Database connection closed');
    process.exit(0);
  }
}

// Run the test
testQuotationNumberGeneration();
