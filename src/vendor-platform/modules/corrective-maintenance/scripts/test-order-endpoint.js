/**
 * Test script to call the order endpoint and verify quotation data is included
 */

const axios = require('axios');

async function testOrderEndpoint() {
  try {
    console.log('🧪 Testing Order Endpoint with Quotation Data');
    console.log('==============================================');

    const baseURL = 'http://localhost:3000';
    const orderId = '683cf8b7545e6ca21d70276a'; // From the original error
    
    console.log('📡 Making API request...');
    console.log(`URL: ${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}`);

    const response = await axios.get(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      {
        headers: {
          'Content-Type': 'application/json',
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ SUCCESS! Response:', response.status);
    console.log('📄 Response data structure:');
    
    const data = response.data.data;
    
    // Check if order data exists
    if (data) {
      console.log('📋 Order ID:', data._id);
      console.log('📋 Order Status:', data.status);
      console.log('📋 Order Type:', data.type);
      
      // Check if quotation data is included
      if (data.quotation) {
        console.log('🎉 QUOTATION DATA FOUND!');
        console.log('📄 Quotation ID:', data.quotation._id);
        console.log('📄 Quotation Number:', data.quotation.quotationNumber || 'NOT GENERATED');
        console.log('📄 Quotation Status:', data.quotation.status);
        console.log('📄 Total Cost:', data.quotation.totalEstimatedCost);
        console.log('📄 Services Count:', data.quotation.services?.length || 0);
        
        // Check if quotationNumber was generated
        if (data.quotation.quotationNumber) {
          console.log('✅ SUCCESS: Quotation number was generated!');
          console.log('📄 Generated number:', data.quotation.quotationNumber);
        } else {
          console.log('❌ ERROR: Quotation number was not generated!');
        }
      } else {
        console.log('❌ No quotation data found in response');
        console.log('📋 This might mean:');
        console.log('   - No quotation exists for this order');
        console.log('   - The order was not found');
        console.log('   - There was an error in the query');
      }
    } else {
      console.log('❌ No order data found in response');
    }

    // Show full response for debugging
    console.log('\n🔍 Full response data:');
    console.log(JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ API call failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.error('🔍 AUTHENTICATION ERROR: Need proper auth token');
      } else if (error.response.status === 404) {
        console.error('🔍 NOT FOUND: Order might not exist or no access');
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Run the test
testOrderEndpoint();
