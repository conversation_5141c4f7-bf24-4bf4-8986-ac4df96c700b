/**
 * Test script to test the startWork endpoint
 */

const axios = require('axios');

async function testStartWork() {
  try {
    console.log('🧪 Testing Start Work Endpoint');
    console.log('===============================');

    const baseURL = 'http://localhost:3000';
    const orderId = '683cf8b7545e6ca21d70276a'; // From the original error
    
    console.log('📡 Making API request...');
    console.log(`URL: ${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/start`);

    const response = await axios.post(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}/start`,
      {}, // Empty body for start work
      {
        headers: {
          'Content-Type': 'application/json',
          // Note: In a real scenario, you'd need proper authentication headers
        }
      }
    );

    console.log('✅ SUCCESS! Response:', response.status);
    console.log('📄 Response data:');
    
    const data = response.data;
    
    if (data.message) {
      console.log('📋 Message:', data.message);
    }
    
    if (data.data) {
      console.log('📋 Order ID:', data.data._id);
      console.log('📋 Order Status:', data.data.status);
      console.log('📋 Start Date:', data.data.startDate);
      
      if (data.data.services && data.data.services.length > 0) {
        console.log('📋 Services:');
        data.data.services.forEach((service, index) => {
          console.log(`   ${index + 1}. ${service.serviceName} - Status: ${service.status}`);
          if (service.actualStartTime) {
            console.log(`      Started at: ${service.actualStartTime}`);
          }
        });
      }
      
      console.log('🎉 Work started successfully!');
    }

    // Show full response for debugging
    console.log('\n🔍 Full response:');
    console.log(JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ API call failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 401) {
        console.error('🔍 AUTHENTICATION ERROR: Need proper auth token');
      } else if (error.response.status === 404) {
        console.error('🔍 NOT FOUND: Order might not exist or no access');
      } else if (error.response.status === 400) {
        console.error('🔍 BAD REQUEST: Check order status and requirements');
        if (error.response.data.message) {
          console.error('Error message:', error.response.data.message);
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Request setup error:', error.message);
    }
  }
}

// Test the workflow step by step
async function testWorkflow() {
  console.log('🔄 Testing Complete Workflow');
  console.log('============================');
  
  const baseURL = 'http://localhost:3000';
  const orderId = '683cf8b7545e6ca21d70276a';
  
  try {
    // Step 1: Check order status
    console.log('\n1️⃣ Checking order status...');
    const orderResponse = await axios.get(
      `${baseURL}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      {
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    if (orderResponse.data.data) {
      console.log('📋 Current order status:', orderResponse.data.data.status);
      console.log('📋 Has quotation:', orderResponse.data.data.quotation ? 'Yes' : 'No');
      
      if (orderResponse.data.data.quotation) {
        console.log('📋 Quotation status:', orderResponse.data.data.quotation.status);
        console.log('📋 Approved services:', 
          orderResponse.data.data.quotation.services?.filter(s => s.isApproved).length || 0
        );
      }
    }
    
    // Step 2: Try to start work
    console.log('\n2️⃣ Attempting to start work...');
    await testStartWork();
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('⚠️  Authentication required - cannot test full workflow');
      console.log('💡 Try testing with proper authentication tokens');
    } else {
      console.error('Error in workflow test:', error.message);
    }
  }
}

// Run the tests
console.log('🚀 Starting StartWork Tests');
console.log('===========================\n');

testWorkflow();
