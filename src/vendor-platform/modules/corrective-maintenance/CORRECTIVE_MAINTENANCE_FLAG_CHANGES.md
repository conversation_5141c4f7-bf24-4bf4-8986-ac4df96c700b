# 🚩 Cambios para la Bandera isCorrectiveMaintenance

Este documento describe todos los cambios realizados para implementar la bandera `isCorrectiveMaintenance` que permite omitir las validaciones de kilometraje en el sistema de appointments.

## 🎯 Objetivo

Cuando llega la bandera `isCorrectiveMaintenance: true` a los endpoints de appointments, el sistema debe:
- ✅ **Omitir validaciones de kilometraje** para mantenimiento preventivo
- ✅ **Omitir validaciones de tiempo** (3 meses mínimos entre mantenimientos)
- ✅ **Permitir agendar inmediatamente** sin restricciones
- ✅ **Devolver service types de mantenimiento correctivo**
- 🚀 **NUEVO: Crear automáticamente todo el flujo de mantenimiento correctivo**

## 📝 Cambios Realizados

### 1. **ServiceType Model Actualizado**
**Archivo:** `src/vendor-platform/modules/serviceType/models/serviceType.model.ts`

**Cambios:**
- ✅ Agregado campo `maintenanceType: 'preventive' | 'corrective'`
- ✅ Agregado `default: 'preventive'` para compatibilidad
- ✅ Actualizada interfaz `IServiceType`

### 2. **ServiceType Controller Actualizado**
**Archivo:** `src/vendor-platform/modules/serviceType/controllers/serviceType.controller.ts`

**Cambios:**
- ✅ Agregado filtro `?maintenanceType=corrective|preventive` en `getAllServicesType`
- ✅ Permite obtener solo service types de mantenimiento correctivo

### 3. **Workshop Appointments Controller Actualizado**
**Archivo:** `src/vendor-platform/modules/workshop/controllers/workshop-appointments.controller.ts`

**Cambios:**
- ✅ Agregado parámetro `isCorrectiveMaintenance` en `createAppointmentToWorkshop`
- ✅ Pasado parámetro al servicio de schedule
- ✅ Agregado filtro `?maintenanceType=corrective|preventive` en endpoints de consulta

### 4. **Schedule Service Actualizado**
**Archivo:** `src/vendor-platform/modules/workshop/services/schedule.service.ts`

**Cambios principales:**

#### `createAppointment` method:
- ✅ Agregado parámetro `isCorrectiveMaintenance?: boolean`
- ✅ Agregados campos adicionales: `failureDescription`, `urgencyLevel`, `customerDescription`
- ✅ Pasado parámetro a `validateAssociateAlreadyHasNearAppointment`
- ✅ Configurado `type: 'corrective'` en appointment data cuando es correctivo
- 🚀 **NUEVO: Llama a `createCorrectiveMaintenanceFlow` automáticamente**

#### `createCorrectiveMaintenanceFlow` method (NUEVO):
- ✅ **Crea orden de mantenimiento correctivo** automáticamente
- ✅ **Completa diagnóstico inicial** basado en el service type
- ✅ **Vincula appointment con orden correctiva** via `correctiveMaintenanceOrderId`
- ✅ **Usa información adicional** del appointment (failureDescription, etc.)
- ✅ **Manejo de errores** sin fallar el appointment

#### `validateAssociateAlreadyHasNearAppointment` method:
- ✅ Agregado parámetro `isCorrectiveMaintenance?: boolean`
- ✅ **Return temprano** cuando `isCorrectiveMaintenance: true`:
  ```typescript
  if (isCorrectiveMaintenance) {
    return {
      maintenanceNumber: 0, // No sigue numeración secuencial
      maintenanceKm: km,
      requiredKm: km,
    };
  }
  ```

#### `getAppointmentInfoByPlatesAndKm` method:
- ✅ Agregado parámetro `isCorrectiveMaintenance?: boolean`
- ✅ **Return temprano** cuando `isCorrectiveMaintenance: true`:
  - Busca service types con `maintenanceType: 'corrective'`
  - Omite todas las validaciones de kilometraje y tiempo
  - Devuelve talleres disponibles sin restricciones

#### `validateMaintenanceRules` method:
- ✅ Agregado parámetro `maintenanceType?: 'preventive' | 'corrective'`
- ✅ **Return temprano** cuando `maintenanceType: 'corrective'`

### 5. **Corrective Maintenance Service Actualizado**
**Archivo:** `src/vendor-platform/modules/corrective-maintenance/services/corrective-maintenance.service.ts`

**Cambios:**
- ✅ Removida creación automática de service types
- ✅ Ahora requiere `serviceTypeId` válido de tipo correctivo
- ✅ Validación que el service type sea de `maintenanceType: 'corrective'`

### 6. **Script de Setup Creado**
**Archivo:** `src/vendor-platform/modules/corrective-maintenance/scripts/setup-corrective-service-types.ts`

**Funcionalidad:**
- ✅ Crea 10 tipos de servicio de mantenimiento correctivo por defecto
- ✅ Se ejecuta una sola vez por organización
- ✅ Incluye: Diagnóstico, Frenos, Llantas, Suspensión, Motor, etc.

## 🔄 Flujos de Uso

### Flujo 1: Endpoint General con Bandera (FLUJO AUTOMÁTICO COMPLETO) 🚀
```javascript
POST /vendor-platform/workshops/WORKSHOP_ID/appointments
{
  "startTime": "2024-01-15T09:00:00.000Z",
  "serviceTypeId": "CORRECTIVE_SERVICE_TYPE_ID",
  "associateId": "CUSTOMER_ID",
  "stockId": "VEHICLE_ID",
  "km": 50000,
  "isCorrectiveMaintenance": true,  // 🚩 BANDERA CLAVE
  // Campos adicionales opcionales
  "failureDescription": "Ruido en los frenos al frenar",
  "urgencyLevel": "high",
  "customerDescription": "El cliente reporta ruido metálico"
}
```

**🎯 Lo que sucede automáticamente:**
1. ✅ Crea el appointment con tipo 'corrective'
2. ✅ Crea automáticamente la orden de mantenimiento correctivo
3. ✅ Completa el diagnóstico inicial basado en el service type
4. ✅ Vincula el appointment con la orden correctiva
5. ✅ Omite todas las validaciones de kilometraje y tiempo

### Flujo 2: Obtener Info con Bandera
```javascript
POST /vendor-platform/schedule/appointment-info
{
  "plates": "ABC-123",
  "currentKm": 50000,
  "isCorrectiveMaintenance": true  // 🚩 BANDERA CLAVE
}
```

### Flujo 3: Endpoint Específico de Corrective Maintenance
```javascript
POST /vendor-platform/corrective-maintenance/orders/ORDER_ID/appointment
{
  "startTime": "2024-01-15T09:00:00.000Z",
  "endTime": "2024-01-15T12:00:00.000Z",
  "serviceTypeId": "CORRECTIVE_SERVICE_TYPE_ID"
}
```

## ✅ Validaciones Omitidas

Cuando `isCorrectiveMaintenance: true`:

### ❌ NO se valida:
- Kilometraje mínimo requerido para mantenimiento
- Tiempo mínimo entre mantenimientos (3 meses)
- Secuencia de números de mantenimiento
- Tipo de servicio específico por marca/kilometraje

### ✅ SÍ se valida:
- Disponibilidad del taller en fecha/hora
- Existencia del vehículo y cliente
- Service type válido y activo
- Permisos de usuario

## 🚀 Cómo Empezar

### 1. Ejecutar Setup (Una sola vez)
```bash
cd src/vendor-platform/modules/corrective-maintenance/scripts
npx ts-node setup-corrective-service-types.ts
```

### 2. Verificar Service Types Creados
```javascript
GET /vendor-platform/organizations/ORG_ID/service-types?maintenanceType=corrective
```

### 3. Usar la Bandera en Appointments
```javascript
POST /vendor-platform/workshops/WORKSHOP_ID/appointments
{
  // ... otros campos
  "isCorrectiveMaintenance": true
}
```

## 🔍 Testing

Para probar que funciona correctamente:

1. **Sin bandera**: Debe aplicar validaciones normales
2. **Con bandera**: Debe omitir validaciones y permitir agendar
3. **Service types**: Debe filtrar correctamente por tipo
4. **Appointments**: Debe marcar como `type: 'corrective'`

## 📚 Documentación

- **Guía Completa**: `QUICK_START_GUIDE.md`
- **README Principal**: `README.md`
- **Este documento**: `CORRECTIVE_MAINTENANCE_FLAG_CHANGES.md`

¡El sistema ahora está completamente preparado para manejar mantenimiento correctivo sin restricciones de kilometraje! 🎉
