# 🚀 Guía de Inicio Rápido - Mantenimiento Correctivo

Esta guía te ayudará a comenzar a usar el sistema de mantenimiento correctivo paso a paso.

## 📋 Prerrequisitos

1. **Base de datos**: Asegúrate de que MongoDB esté corriendo
2. **Servidor**: El servidor backend debe estar ejecutándose
3. **Autenticación**: Necesitas un token válido de vendor platform

## 🔧 Paso 1: Configurar Service Types de Mantenimiento Correctivo

Primero, necesitas crear los tipos de servicio para mantenimiento correctivo en tu organización.

### Opción A: Usar el Script Automático (Recomendado)

```bash
# Ejecutar el script para crear service types por defecto
cd src/vendor-platform/modules/corrective-maintenance/scripts
npx ts-node setup-corrective-service-types.ts
```

### Opción B: Crear Manualmente via API

```javascript
POST /vendor-platform/organizations/:organizationId/service-types
Authorization: Bearer YOUR_TOKEN

{
  "name": "Diagnóstico General",
  "description": "Diagnóstico completo del vehículo para identificar fallas",
  "duration": 120,
  "maintenanceType": "corrective",
  "price": 500,
  "includedServices": ["diagnostic", "inspection", "report"]
}
```

## 🚗 Paso 2: Crear una Orden de Mantenimiento Correctivo

```javascript
POST /vendor-platform/corrective-maintenance/orders
Authorization: Bearer YOUR_TOKEN

{
  "stockId": "VEHICLE_ID",
  "associateId": "CUSTOMER_ID",
  "workshopId": "WORKSHOP_ID",
  "type": "customer-initiated",
  "failureType": "known",
  "arrivalMethod": "driving",
  "customerDescription": "El vehículo hace ruido en los frenos al frenar",
  "canVehicleDrive": true,
  "needsTowTruck": false,
  "approvalType": "fleet"
}
```

**Respuesta esperada:**
```json
{
  "message": "Corrective maintenance order created successfully",
  "data": {
    "_id": "ORDER_ID",
    "stockId": "VEHICLE_ID",
    "status": "pending",
    "type": "customer-initiated",
    ...
  }
}
```

## 🔍 Paso 3: Completar el Diagnóstico

```javascript
POST /vendor-platform/corrective-maintenance/orders/ORDER_ID/diagnosis
Authorization: Bearer YOUR_TOKEN
Content-Type: multipart/form-data

{
  "diagnosisNotes": "Se identificó desgaste en las pastillas de freno delanteras",
  "services": [
    {
      "serviceType": "brakes",
      "serviceName": "Cambio de Pastillas de Freno",
      "description": "Cambio de pastillas delanteras y revisión de discos",
      "estimatedCost": 2500,
      "estimatedDuration": 3,
      "laborCost": 1500,
      "parts": [
        {
          "name": "Pastillas de freno delanteras",
          "quantity": 1,
          "unitCost": 800,
          "supplier": "Bosch"
        }
      ]
    }
  ]
}
```

## 💰 Paso 4: Crear Cotización

```javascript
POST /vendor-platform/corrective-maintenance/orders/ORDER_ID/quotation
Authorization: Bearer YOUR_TOKEN

{
  "approvalType": "fleet",
  "validityDays": 15,
  "customerNotes": "Cotización para reparación de frenos",
  "paymentTerms": "Pago contra entrega"
}
```

## ✅ Paso 5: Enviar para Aprobación

```javascript
POST /vendor-platform/corrective-maintenance/quotations/QUOTATION_ID/submit
Authorization: Bearer YOUR_TOKEN
```

## 👍 Paso 6: Procesar Aprobación

```javascript
POST /vendor-platform/corrective-maintenance/quotations/QUOTATION_ID/approve
Authorization: Bearer YOUR_TOKEN

{
  "decisions": [
    {
      "serviceId": "SERVICE_ID",
      "isApproved": true
    }
  ]
}
```

## 📅 Paso 7: Crear Cita de Trabajo

### Opción A: Usando el endpoint específico de mantenimiento correctivo

Primero, obtén los service types de mantenimiento correctivo disponibles:

```javascript
GET /vendor-platform/organizations/ORGANIZATION_ID/service-types?maintenanceType=corrective
Authorization: Bearer YOUR_TOKEN
```

Luego crea la cita:

```javascript
POST /vendor-platform/corrective-maintenance/orders/ORDER_ID/appointment
Authorization: Bearer YOUR_TOKEN

{
  "startTime": "2024-01-15T09:00:00.000Z",
  "endTime": "2024-01-15T12:00:00.000Z",
  "serviceTypeId": "CORRECTIVE_SERVICE_TYPE_ID",
  "failureDescription": "Cambio de pastillas de freno",
  "urgencyLevel": "medium"
}
```

### Opción B: Usando el endpoint general con bandera isCorrectiveMaintenance (FLUJO AUTOMÁTICO)

También puedes usar el endpoint general de appointments con la bandera `isCorrectiveMaintenance`. **¡Esta opción crea automáticamente todo el flujo de mantenimiento correctivo!**

```javascript
POST /vendor-platform/workshops/WORKSHOP_ID/appointments
Authorization: Bearer YOUR_TOKEN

{
  "startTime": "2024-01-15T09:00:00.000Z",
  "serviceTypeId": "CORRECTIVE_SERVICE_TYPE_ID",
  "associateId": "CUSTOMER_ID",
  "stockId": "VEHICLE_ID",
  "km": 50000,
  "isCorrectiveMaintenance": true,
  // Campos opcionales adicionales
  "failureDescription": "Ruido en los frenos al frenar",
  "urgencyLevel": "high",
  "customerDescription": "El cliente reporta ruido metálico"
}
```

**🚀 FLUJO AUTOMÁTICO - Lo que sucede cuando usas `isCorrectiveMaintenance: true`:**

1. ✅ **Crea el appointment** con tipo 'corrective'
2. ✅ **Crea automáticamente la orden de mantenimiento correctivo**
3. ✅ **Completa el diagnóstico inicial** basado en el service type
4. ✅ **Vincula el appointment con la orden correctiva**
5. ✅ **Omite validaciones de kilometraje** para mantenimiento preventivo
6. ✅ **Omite validaciones de tiempo** (3 meses mínimos)
7. ✅ **Permite agendar inmediatamente** sin restricciones

**Ventajas del flujo automático:**
- 🎯 **Un solo endpoint** crea todo el flujo
- ⚡ **Más rápido** que crear cada paso manualmente
- 🔗 **Vinculación automática** entre appointment y orden correctiva
- 📋 **Diagnóstico inicial** basado en el service type seleccionado
- 🚨 **Ideal para emergencias** y reparaciones urgentes

## 🔧 Paso 8: Iniciar Trabajo

```javascript
POST /vendor-platform/corrective-maintenance/orders/ORDER_ID/start
Authorization: Bearer YOUR_TOKEN
```

## 📊 Consultar Estado y Progreso

### Ver todas las órdenes
```javascript
GET /vendor-platform/corrective-maintenance/orders
Authorization: Bearer YOUR_TOKEN
```

### Ver órdenes por estado
```javascript
GET /vendor-platform/corrective-maintenance/orders?status=in-progress
Authorization: Bearer YOUR_TOKEN
```

### Ver cotizaciones
```javascript
GET /vendor-platform/corrective-maintenance/quotations
Authorization: Bearer YOUR_TOKEN
```

### Ver citas de mantenimiento correctivo
```javascript
GET /vendor-platform/workshops/WORKSHOP_ID/appointments?maintenanceType=corrective
Authorization: Bearer YOUR_TOKEN
```

### Obtener información de appointment para mantenimiento correctivo
```javascript
POST /vendor-platform/schedule/appointment-info
Authorization: Bearer YOUR_TOKEN

{
  "plates": "ABC-123",
  "currentKm": 50000,
  "isCorrectiveMaintenance": true
}
```

**Con `isCorrectiveMaintenance: true` obtienes:**
- Service types de mantenimiento correctivo disponibles
- Talleres disponibles sin restricciones de kilometraje
- No se aplican validaciones de mantenimiento preventivo

## 🎯 Flujo Completo de Ejemplo

### Opción 1: Flujo Manual (Paso a Paso)
```bash
# 1. Configurar service types (una sola vez)
npx ts-node setup-corrective-service-types.ts

# 2. Crear orden
curl -X POST "http://localhost:3000/vendor-platform/corrective-maintenance/orders" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "stockId": "VEHICLE_ID",
    "associateId": "CUSTOMER_ID",
    "workshopId": "WORKSHOP_ID",
    "type": "customer-initiated",
    "failureType": "known",
    "arrivalMethod": "driving",
    "customerDescription": "Ruido en frenos",
    "canVehicleDrive": true,
    "needsTowTruck": false,
    "approvalType": "fleet"
  }'

# 3. Completar diagnóstico (usar ORDER_ID de la respuesta anterior)
# 4. Crear cotización
# 5. Aprobar servicios
# 6. Crear cita
# 7. Ejecutar trabajo
```

### Opción 2: Flujo Automático (Un Solo Paso) ⚡
```bash
# 1. Configurar service types (una sola vez)
npx ts-node setup-corrective-service-types.ts

# 2. Obtener service types de mantenimiento correctivo
curl -X GET "http://localhost:3000/vendor-platform/organizations/ORG_ID/service-types?maintenanceType=corrective" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. Crear appointment con flujo automático completo
curl -X POST "http://localhost:3000/vendor-platform/workshops/WORKSHOP_ID/appointments" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startTime": "2024-01-15T09:00:00.000Z",
    "serviceTypeId": "CORRECTIVE_SERVICE_TYPE_ID",
    "associateId": "CUSTOMER_ID",
    "stockId": "VEHICLE_ID",
    "km": 50000,
    "isCorrectiveMaintenance": true,
    "failureDescription": "Ruido en los frenos al frenar",
    "urgencyLevel": "high",
    "customerDescription": "El cliente reporta ruido metálico al frenar"
  }'

# ¡Eso es todo! El sistema automáticamente:
# - Crea el appointment
# - Crea la orden de mantenimiento correctivo
# - Completa el diagnóstico inicial
# - Vincula todo el flujo
```

**🚀 Recomendación: Usa la Opción 2 (Flujo Automático) para mayor eficiencia!**

## 🚨 Solución de Problemas Comunes

### Error: "Service type not found"
- Asegúrate de haber ejecutado el script de setup o creado service types manualmente
- Verifica que el `serviceTypeId` sea válido y de tipo `corrective`

### Error: "Workshop not found"
- Verifica que el `workshopId` exista y esté activo
- Asegúrate de que el workshop pertenezca a tu organización

### Error: "Vehicle not found"
- Verifica que el `stockId` sea válido
- Asegúrate de que el vehículo esté disponible para mantenimiento

### Error: "Time slot not available"
- Verifica la disponibilidad del taller en la fecha/hora solicitada
- Considera usar horarios con menos demanda

## 📞 Soporte

Si encuentras problemas:
1. Revisa los logs del servidor para errores detallados
2. Verifica que todos los IDs sean válidos
3. Asegúrate de tener los permisos correctos
4. Consulta la documentación completa en el README.md

¡Listo! Ahora puedes usar el sistema completo de mantenimiento correctivo. 🎉
