import { Types } from 'mongoose';
import { Quotation, QuotationStatus } from '../models/quotation.model';
import {
  CorrectiveMaintenanceOrder,
  CorrectiveMaintenanceStatus,
} from '../models/corrective-maintenance-order.model';
import vendorDB from '@/vendor-platform/db';

// Mock data for testing
const mockOrderData = {
  _id: new Types.ObjectId(),
  stockId: new Types.ObjectId(),
  associateId: new Types.ObjectId(),
  organizationId: new Types.ObjectId(),
  workshopId: new Types.ObjectId(),
  type: 'emergency',
  status: CorrectiveMaintenanceStatus.DIAGNOSED,
  failureType: 'mechanical',
  arrivalMethod: 'driving',
  customerDescription: 'Test description',
  canVehicleDrive: true,
  needsTowTruck: false,
  approvalType: 'fleet',
  services: [new Types.ObjectId()],
  diagnosisEvidence: {
    photos: [],
    videos: [],
    reports: [],
  },
};

const mockQuotationData = {
  orderId: mockOrderData._id,
  status: QuotationStatus.DRAFT,
  version: 1,
  services: [
    {
      serviceId: new Types.ObjectId(),
      serviceName: 'Test Service',
      description: 'Test service description',
      estimatedCost: 1000,
      estimatedDuration: 24,
      laborCost: 500,
      partsCost: 500,
      isApproved: false,
      parts: [],
      slaEstimate: 24,
      partsAvailabilityDelay: 0,
    },
  ],
  totalEstimatedCost: 1000,
  totalEstimatedDuration: 24,
  totalLaborCost: 500,
  totalPartsCost: 500,
  approvalRequired: true,
  approvalType: 'fleet' as const,
  approvedServices: [],
  rejectedServices: [],
  validUntil: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
  overallSLA: 24,
  earliestStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
  estimatedCompletionDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
  diagnosticEvidence: {
    photos: [],
    videos: [],
    reports: [],
  },
};

describe('Quotation Model', () => {
  beforeAll(async () => {
    // Ensure database connection
    if (vendorDB.readyState !== 1) {
      await vendorDB.asPromise();
    }
  });

  afterAll(async () => {
    // Clean up test data
    await Quotation.deleteMany({ orderId: mockOrderData._id });
    await CorrectiveMaintenanceOrder.deleteMany({ _id: mockOrderData._id });
  });

  describe('Quotation Number Generation', () => {
    it('should automatically generate a quotation number when creating a new quotation', async () => {
      // Create a test order first
      const order = new CorrectiveMaintenanceOrder(mockOrderData);
      await order.save();

      // Create quotation without quotationNumber
      const quotation = new Quotation(mockQuotationData);

      // Verify quotationNumber is not set before saving
      expect(quotation.quotationNumber).toBeUndefined();

      // Save the quotation
      await quotation.save();

      // Verify quotationNumber was generated
      expect(quotation.quotationNumber).toBeDefined();
      expect(quotation.quotationNumber).toMatch(/^COT-\d{6}-\d{6}$/);

      console.log('Generated quotation number:', quotation.quotationNumber);
    });

    it('should not override existing quotation number', async () => {
      const customQuotationNumber = 'COT-202412-999999';

      // Create quotation with custom quotationNumber
      const quotationData = {
        ...mockQuotationData,
        quotationNumber: customQuotationNumber,
      };

      const quotation = new Quotation(quotationData);
      await quotation.save();

      // Verify custom quotationNumber was preserved
      expect(quotation.quotationNumber).toBe(customQuotationNumber);
    });

    it('should generate unique quotation numbers for multiple quotations', async () => {
      const quotationNumbers = new Set();

      // Create multiple quotations
      for (let i = 0; i < 3; i++) {
        const orderData = {
          ...mockOrderData,
          _id: new Types.ObjectId(),
        };

        const order = new CorrectiveMaintenanceOrder(orderData);
        await order.save();

        const quotationData = {
          ...mockQuotationData,
          orderId: orderData._id,
        };

        const quotation = new Quotation(quotationData);
        await quotation.save();

        expect(quotation.quotationNumber).toBeDefined();
        expect(quotationNumbers.has(quotation.quotationNumber)).toBe(false);
        quotationNumbers.add(quotation.quotationNumber);

        console.log(`Quotation ${i + 1} number:`, quotation.quotationNumber);
      }

      expect(quotationNumbers.size).toBe(3);
    });
  });

  describe('Pre-save Middleware', () => {
    it('should calculate totals correctly', async () => {
      const quotationData = {
        ...mockQuotationData,
        orderId: new Types.ObjectId(),
        services: [
          {
            serviceId: new Types.ObjectId(),
            serviceName: 'Service 1',
            description: 'Description 1',
            estimatedCost: 1000,
            estimatedDuration: 24,
            laborCost: 600,
            partsCost: 400,
            isApproved: false,
            parts: [],
            slaEstimate: 24,
            partsAvailabilityDelay: 0,
          },
          {
            serviceId: new Types.ObjectId(),
            serviceName: 'Service 2',
            description: 'Description 2',
            estimatedCost: 2000,
            estimatedDuration: 48,
            laborCost: 1200,
            partsCost: 800,
            isApproved: false,
            parts: [],
            slaEstimate: 48,
            partsAvailabilityDelay: 12,
          },
        ],
      };

      const quotation = new Quotation(quotationData);
      await quotation.save();

      // Verify calculated totals
      expect(quotation.totalEstimatedCost).toBe(3000);
      expect(quotation.totalEstimatedDuration).toBe(72);
      expect(quotation.totalLaborCost).toBe(1800);
      expect(quotation.totalPartsCost).toBe(1200);
      expect(quotation.overallSLA).toBe(60); // Max of (24 + 0) and (48 + 12)
    });
  });
});
