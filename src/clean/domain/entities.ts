import { Referrer } from '@/models/admissionRequestSchema';
import { DEFAULT_PAGINATION_LIMIT, MAX_PAGINATION_LIMIT } from '../../constants';
import {
  AdmissionRequestStatus,
  Country,
  CurrencyCode,
  GigPlatform,
  HomeVisitStatus,
  MediaStatus,
  MediaType,
  PalencaAccountStatus,
  PalencaWebhookAction,
  EarningsAnalysisStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
  RequestPersonalDataStepStatus,
  ResidentOwnershipStatus,
  AdmissionRequestDocumentType,
  AdmissionRequestRejectionReason,
  EntityType,
  ActionType,
  AnalysisStatus,
  RiskCategory,
  ScorecardVariableName,
  ScorecardVersion,
  ScorecardVariableCategoryType,
  PalencaRetrievalStatus,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestAdditionalDocumentType,
  MLModels,
} from './enums';
import { Types } from 'mongoose';

export interface TimestampProps {
  createdAt?: Date;
  updatedAt?: Date;
}

export class Document {
  id?: string | undefined;

  fileName: string;

  path: string;

  type: MediaType;

  status: MediaStatus;

  mimeType?: string;

  url?: string | null;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Document) {
    this.id = props.id;
    this.fileName = props.fileName;
    this.path = props.path;
    this.type = props.type;
    this.status = props.status;
    this.mimeType = props.mimeType;
    this.url = props.url;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
export class RequestPersonalData {
  status?: RequestPersonalDataStepStatus | null;

  firstName?: string | null;

  lastName?: string | null;

  phone?: string | null;

  email?: string | null;

  birthdate?: string | null;

  taxId?: string | null;

  nationalId?: string | null;

  postalCode?: string | null;

  city?: string | null;

  state?: string | null;

  neighborhood?: string | null;

  street?: string | null;

  streetNumber?: string | null;

  department?: string | null;

  country?: keyof typeof Country | null;

  vehicleSelected?: string;

  ssn?: string | null;

  rideShareTotalRides?: number | null;

  avgEarningPerWeek?: number | null;

  termsAndConditions?: boolean | null;

  dataPrivacyConsentForm?: boolean | null;

  nationality?: string | null;

  age?: number | null;

  occupation?: string | null;

  homePhone?: string | null;

  timeInResidency?: string | null;

  municipality?: string | null;

  maritalStatus?: string | null;

  dependents?: string | null;

  spouseOrPartnerIncome?: string | null;

  partnerSourceOfIncome?: string | null;

  partnerName?: string | null;

  partnerPhone?: string | null;

  noOfDependents?: number | null;

  dependendsInfo?: Array<{
    dependendName: string;
    dependendPhone: string | null;
    dependentRelationship: string;
  }> | null;

  ownACar?: string | null;

  carLeasingtime?: string | null;

  carMake?: string | null;

  carModel?: string | null;

  ownDebt?: string | null;

  outStandingDebt?: string | null;

  doesDebtAffectPersonalFinance?: string | null;

  references?: {
    reference1Name: string;
    reference1Phone: string;
    reference1Relationship: string;
    reference1Address: string;
    reference2Name: string;
    reference2Phone: string;
    reference2Relationship: string;
    reference2Address: string;
    reference3Name: string;
    reference3Phone: string;
    reference3Relationship: string;
    reference3Address: string;
  } | null;

  learnAboutOcn?: string | null;

  referrer?: Referrer | null;

  doesItApplyToElectricCars?: string | null;

  ficoScore?: string | null;

  criminalBackgroundCheck?: string | null;

  motorVehicleRecordCheck?: string | null;

  privacyPolicy?: boolean | null;

  ocnBackgroundAndCreditCheckForApplication?: boolean | null;

  constructor(props: RequestPersonalData) {
    this.status = props.status;
    this.firstName = props.firstName || null;
    this.lastName = props.lastName || null;
    this.phone = props.phone || null;
    this.email = props.email || null;
    this.birthdate = props.birthdate || null;
    this.taxId = props.taxId || null;
    this.nationalId = props.nationalId || null;
    this.postalCode = props.postalCode || null;
    this.city = props.city || null;
    this.state = props.state || null;
    this.neighborhood = props.neighborhood || null;
    this.street = props.street || null;
    this.streetNumber = props.streetNumber || null;
    this.department = props.department || null;
    this.country = props.country || null;
    this.vehicleSelected = props.vehicleSelected;
    this.ssn = props.ssn || null;
    this.rideShareTotalRides = props.rideShareTotalRides || null;
    this.avgEarningPerWeek = props.avgEarningPerWeek || null;
    this.termsAndConditions = props.termsAndConditions || null;
    this.dataPrivacyConsentForm = props.dataPrivacyConsentForm || null;
    this.nationality = props.nationality || null;
    this.age = props.age || null;
    this.occupation = props.occupation || null;
    this.homePhone = props.homePhone || null;
    this.timeInResidency = props.timeInResidency || null;
    this.municipality = props.municipality || null;
    this.maritalStatus = props.maritalStatus;
    this.dependents = props.dependents || null;
    this.spouseOrPartnerIncome = props.spouseOrPartnerIncome || null;
    this.partnerSourceOfIncome = props.partnerSourceOfIncome || null;
    this.partnerName = props.partnerName || null;
    this.partnerPhone = props.partnerPhone || null;
    this.noOfDependents = props.noOfDependents || null;
    this.dependendsInfo = props.dependendsInfo || null;
    this.ownACar = props.ownACar || null;
    this.carLeasingtime = props.carLeasingtime || null;
    this.carMake = props.carMake || null;
    this.carModel = props.carModel || null;
    this.ownDebt = props.ownDebt || null;
    this.outStandingDebt = props.outStandingDebt || null;
    this.doesDebtAffectPersonalFinance = props.doesDebtAffectPersonalFinance || null;
    this.references = props.references || null;
    this.learnAboutOcn = props.learnAboutOcn || null;
    this.referrer = props.referrer || null;
    this.doesItApplyToElectricCars = props.doesItApplyToElectricCars || null;
    this.ficoScore = props.ficoScore || null;
    this.criminalBackgroundCheck = props.criminalBackgroundCheck || null;
    this.motorVehicleRecordCheck = props.motorVehicleRecordCheck || null;
    this.privacyPolicy = props.privacyPolicy || null;
    this.ocnBackgroundAndCreditCheckForApplication = props.ocnBackgroundAndCreditCheckForApplication || null;
  }
}

export class RequestDocument {
  mediaId?: string | null;

  status: RequestDocumentStatus;

  media?: Document | null;

  type:
    | AdmissionRequestDocumentType
    | AdmissionRequestDocumentTypeUS
    | AdmissionRequestAdditionalDocumentType;

  constructor(props: RequestDocument) {
    this.mediaId = props.mediaId || null;
    this.status = props.status || RequestDocumentStatus.pending;
    this.media = props.media || null;
    this.type = props.type;
  }
}

export class RequestDocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus;

  documents: RequestDocument[];

  constructor(props: RequestDocumentsAnalysis) {
    this.status = props.status;
    this.documents = props.documents;
  }
}

export class PlatformMetric {
  platform?: string;

  requestId?: string;

  acceptanceRate: number;

  cancellationRate: number;

  rating: number;

  lifetimeTrips: number;

  timeSinceFirstTrip: number;

  activationStatus?: string;

  constructor(props: PlatformMetric) {
    this.platform = props.platform;
    this.requestId = props.requestId;
    this.acceptanceRate = props.acceptanceRate;
    this.cancellationRate = props.cancellationRate;
    this.rating = props.rating;
    this.lifetimeTrips = props.lifetimeTrips;
    this.timeSinceFirstTrip = props.timeSinceFirstTrip;
    this.activationStatus = props.activationStatus;
  }
}

export class PalencaAccountRetrieval {
  status: PalencaRetrievalStatus;

  constructor(props: PalencaAccountRetrieval) {
    this.status = props.status;
  }
}

export class PalencaAccount {
  accountId: string;

  platform: GigPlatform;

  status: PalencaAccountStatus;

  earnings: PalencaAccountRetrieval;

  metrics: PalencaAccountRetrieval;

  createdAt: Date;

  constructor(props: PalencaAccount) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.status = props.status;
    this.earnings = props.earnings;
    this.metrics = props.metrics;
    this.createdAt = props.createdAt;
  }
}
export class RequestPalenca {
  widgetId: string; // Its needed to know which Palenca widget was used and listen to the correct webhook

  externalId: string; // This is the same as our customerId, the only way we can map a Palenca customer with our customer is through this field

  accounts: PalencaAccount[]; // We keep track of the accounts that the customer has connected through Palenca

  constructor(props: RequestPalenca) {
    this.widgetId = props.widgetId;
    this.externalId = props.externalId;
    this.accounts = props.accounts;
  }
}

export class HomeVisit {
  isAddressProvidedByApplicant?: boolean;

  residentOwnershipStatus?: ResidentOwnershipStatus;

  hasGarage?: boolean;

  comments?: string;

  images?: string[];

  media?: Document[] | null;

  status?: HomeVisitStatus;

  responsible?: string;

  visitDate?: Date | null;

  visitTime?: string | null;

  houseInformation?: {
    ownProperty: string;
    nameOfOwner: string;
    ownerRelative: string;
    ownerRelativeRelation: string;
    ownerPhone: string;
    typeOfHousing: string;
    noOfBedrooms: number;
    livingRoom: string;
    dinningRoom: string;
    kitchen: string;
    television: string;
    audioSystem: string;
    stove: string;
    refrigerator: string;
    washingMachine: string;
  } | null;

  proofOfPropertyOwnership?: string[];

  visitorEmailAddress?: string;

  doesProofOfAddressMatchLocation?: string;

  characteristicsOfGarage?: string;

  behaviourOfCustomerDuringCall?: string;

  suggestedStatus?: string;

  statusReason?: string;

  homeVisitStepsStatus?: {
    personal?: string;
    contact?: string;
    address?: string;
    family?: string;
    property?: string;
    automobile?: string;
    debt?: string;
    references?: string;
    outcome?: string;
  };

  reasonOfRejection?: string;

  homeImages?: {
    homeImagesFront?: string[];
    homeImagesGarage?: string[];
    homeImagesSurroundings?: string[];
  };

  constructor(props: HomeVisit) {
    this.isAddressProvidedByApplicant = props.isAddressProvidedByApplicant;
    this.residentOwnershipStatus = props.residentOwnershipStatus;
    this.hasGarage = props.hasGarage;
    this.comments = props.comments;
    this.images = props.images;
    this.status = props.status;
    this.responsible = props.responsible;
    this.visitDate = props.visitDate;
    this.media = props.media || null;
    this.visitTime = props.visitTime || null;
    this.houseInformation = props.houseInformation || null;
    this.proofOfPropertyOwnership = props.proofOfPropertyOwnership;
    this.visitorEmailAddress = props.visitorEmailAddress;
    this.doesProofOfAddressMatchLocation = props.doesProofOfAddressMatchLocation;
    this.characteristicsOfGarage = props.characteristicsOfGarage;
    this.behaviourOfCustomerDuringCall = props.behaviourOfCustomerDuringCall;
    this.homeVisitStepsStatus = props.homeVisitStepsStatus;
    this.reasonOfRejection = props.reasonOfRejection;
    this.suggestedStatus = props.suggestedStatus;
    this.statusReason = props.statusReason;
    this.homeImages = props.homeImages;
  }
}

export class EarningsAnalysis {
  totalEarnings?: number | null;

  earnings?: WeeklyEarning[] | null;

  platforms?: number | null;

  status: EarningsAnalysisStatus;

  // We need to know when the user started the analysis
  // to know if we need to retrieve the earnings if we did not receive the webhook
  startedAt?: Date;

  constructor(props: EarningsAnalysis) {
    this.status = props.status;
    this.totalEarnings = props.totalEarnings;
    this.earnings = props.earnings;
    this.platforms = props.platforms;
    this.startedAt = props.startedAt;
  }
}

export class AdmissionRequest implements TimestampProps {
  id?: string | undefined;

  status: AdmissionRequestStatus;

  convertedToAssociate?: boolean | null;

  rejectionReason?: AdmissionRequestRejectionReason | null;

  personalData: RequestPersonalData;

  documentsAnalysis: RequestDocumentsAnalysis;

  palenca: RequestPalenca;

  homeVisit?: HomeVisit | null;

  earningsAnalysis: EarningsAnalysis;

  riskAnalysis: RiskAnalysis;

  modelScores?: ModelScores;

  hubspot?: {
    id?: string;
  };

  hilos?: object;

  hubspotDeal?: {
    id?: string;
  };

  createdAt?: Date;

  updatedAt?: Date;

  screenshots?: Object;

  source?: string | null;

  clientIpAddress?: string | null;

  homeVisitScheduleLinkSendDate?: Date | null;

  avalData?: RequestAvalData | null;

  locationData?: RequestLocationData | null;

  associateId?: string | null;

  typeOfPreapproval?: string | null;

  agentId?: Types.ObjectId;

  constructor(props: AdmissionRequest) {
    this.id = props.id;
    this.status = props.status;
    this.convertedToAssociate = props.convertedToAssociate || false;
    this.rejectionReason = props.rejectionReason || null;
    this.personalData = props.personalData;
    this.documentsAnalysis = props.documentsAnalysis;
    this.palenca = props.palenca;
    this.homeVisit = props.homeVisit || null;
    this.earningsAnalysis = props.earningsAnalysis;
    this.riskAnalysis = props.riskAnalysis;
    this.modelScores = props.modelScores;
    this.hubspot = props.hubspot;
    this.hubspotDeal = props.hubspotDeal;
    this.createdAt = props.createdAt || new Date();
    this.updatedAt = props.updatedAt || new Date();
    this.screenshots = props.screenshots || [];
    this.source = props.source;
    this.clientIpAddress = props.clientIpAddress;
    this.homeVisitScheduleLinkSendDate = props.homeVisitScheduleLinkSendDate;
    this.avalData = props.avalData;
    this.locationData = props.locationData;
    this.associateId = props.associateId;
    this.typeOfPreapproval = props.typeOfPreapproval;
    this.agentId = props.agentId;
  }
}

export class PalencaWebhook {
  webhookAction: PalencaWebhookAction;

  userId: string;

  accountId: string;

  // This is the same as our customerId, the only way we can map
  // a Palenca customer with our customer is through this field
  externalId: string;

  country: Country;

  platform: GigPlatform;

  statusDetails?: string | null;

  constructor(props: PalencaWebhook) {
    this.webhookAction = props.webhookAction;
    this.userId = props.userId;
    this.accountId = props.accountId;
    this.externalId = props.externalId;
    this.country = props.country;
    this.platform = props.platform;
    this.statusDetails = props.statusDetails;
  }
}

export class ModelResult {
  modelName: MLModels;

  status: AnalysisStatus;

  modelScore: number;

  modelFeatureScores: object;

  modelWeights: object;

  modelResult: object;

  constructor(props: ModelResult) {
    this.status = props.status;
    this.modelName = props.modelName;
    this.modelScore = props.modelScore;
    this.modelFeatureScores = props.modelFeatureScores;
    this.modelWeights = props.modelWeights;
    this.modelResult = props.modelResult;
  }
}

export class ModelScores {
  [MLModels.RIDESHARE_PERFORMANCE]: ModelResult;

  [MLModels.FINANCIAL_ASSESSMENT]: ModelResult;

  [MLModels.PERSONAL_INFORMATION]: ModelResult;

  [MLModels.HOMEVISIT_INFORMATION]: ModelResult;

  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: ModelResult;

  constructor(props: ModelScores) {
    this[MLModels.RIDESHARE_PERFORMANCE] = props[MLModels.RIDESHARE_PERFORMANCE];
    this[MLModels.FINANCIAL_ASSESSMENT] = props[MLModels.FINANCIAL_ASSESSMENT];
    this[MLModels.PERSONAL_INFORMATION] = props[MLModels.PERSONAL_INFORMATION];
    this[MLModels.HOMEVISIT_INFORMATION] = props[MLModels.HOMEVISIT_INFORMATION];
    // this[MLModels.DRIVING_AND_LEGAL_HISTORY] = props[MLModels.DRIVING_AND_LEGAL_HISTORY];
  }
}

export class DailyEarning {
  amount: number;

  countTrips: number;

  earningDate: Date;

  currency: CurrencyCode;

  constructor(props: DailyEarning) {
    this.amount = props.amount;
    this.countTrips = props.countTrips;
    this.earningDate = props.earningDate;
    this.currency = props.currency;
  }
}
export class WeeklyEarning {
  totalAmount: number;

  totalTrips: number;

  fromDate: Date;

  toDate: Date;

  week: number;

  year: number;

  currency: CurrencyCode;

  dailyEarnings: DailyEarning[];

  constructor(props: WeeklyEarning) {
    this.totalAmount = props.totalAmount;
    this.totalTrips = props.totalTrips;
    this.fromDate = props.fromDate;
    this.toDate = props.toDate;
    this.week = props.week;
    this.year = props.year;
    this.currency = props.currency;
    this.dailyEarnings = props.dailyEarnings;
  }
}

export class Earning {
  id?: string | undefined;

  amount: number;

  currency: CurrencyCode;

  earningDate: Date;

  countTrips: number;

  requestId: string;

  cashAmount?: number;

  platform: GigPlatform;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Earning) {
    this.id = props.id;
    this.amount = props.amount;
    this.currency = props.currency;
    this.earningDate = props.earningDate;
    this.countTrips = props.countTrips;
    this.requestId = props.requestId;
    this.platform = props.platform;
    this.cashAmount = props.cashAmount;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}
export class Metric {
  id?: string | undefined;

  requestId: string;

  acceptanceRate: number;

  cancellationRate: number;

  rating: number;

  lifetimeTrips: number;

  timeSinceFirstTrip: number;

  activationStatus: string;

  platform: GigPlatform;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Metric) {
    this.id = props.id;
    this.requestId = props.requestId;
    this.acceptanceRate = props.acceptanceRate;
    this.cancellationRate = props.cancellationRate;
    this.rating = props.rating;
    this.lifetimeTrips = props.lifetimeTrips;
    this.timeSinceFirstTrip = props.timeSinceFirstTrip;
    this.activationStatus = props.activationStatus;
    this.platform = props.platform;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}

export class PaginationSearchOptions {
  page: number;

  itemsPerPage: number;

  constructor(props: PaginationSearchOptions) {
    this.page = props.page || 1;
    this.itemsPerPage = props.itemsPerPage || DEFAULT_PAGINATION_LIMIT;

    if (this.itemsPerPage > MAX_PAGINATION_LIMIT) {
      this.itemsPerPage = MAX_PAGINATION_LIMIT;
    }
  }
}

export class Pagination {
  hasPrevious: boolean;

  hasMore: boolean;

  page: number;

  totalItems: number;

  totalPages: number;

  constructor(props: Pagination) {
    this.hasPrevious = props.hasPrevious;
    this.hasMore = props.hasMore;
    this.page = props.page;
    this.totalItems = props.totalItems;
    this.totalPages = props.totalPages;
  }
}

export class Event {
  id?: string | undefined;

  userId?: string;

  entityId: string;

  entityType: EntityType;

  actionType: ActionType;

  message: string;

  createdAt?: Date;

  user?: User | null;

  constructor(props: Event) {
    this.id = props.id;
    this.userId = props.userId;
    this.entityId = props.entityId;
    this.entityType = props.entityType;
    this.actionType = props.actionType;
    this.message = props.message;
    this.createdAt = props.createdAt || new Date();
    this.user = props.user || null;
  }
}

export class User {
  id?: string | undefined;

  name: string;

  image?: string;

  constructor(props: User) {
    this.id = props.id;
    this.name = props.name;
    this.image = props.image;
  }
}

/**
 * *** DEPRECATED ***
 * Represents a category within a scorecard variable.
 * Includes details such as threshold for evaluation, risk category, risk score, and weight.
 * Used for defining the scorecard's structure and scoring parameters.
 */
export class ScorecardVariableCategory {
  threshold: { min: number; max: number } | string;

  riskCategory: RiskCategory;

  riskScore: number;

  weight: number;

  constructor(props: ScorecardVariableCategory) {
    this.threshold = props.threshold;
    this.riskCategory = props.riskCategory;
    this.riskScore = props.riskScore;
    this.weight = props.weight;
  }
}

/**
 * *** DEPRECATED ***
 * Extends the ScorecardVariableCategory with a 'result' property.
 * Used in the context of a ScorecardDetail to store the calculated result
 * for a specific category, reflecting its contribution to the total score.
 */
export class ScorecardDetailCategory extends ScorecardVariableCategory {
  result: number;

  constructor(props: ScorecardDetailCategory) {
    super(props);
    this.result = props.result;
  }
}

/**
 * *** DEPRECATED ***
 * Represents a variable in the scorecard.
 * Contains a name identifier for the variable and an array of categories,
 * each of which represents a possible classification within the variable.
 */
export class ScorecardVariable {
  name: ScorecardVariableName;

  categories: ScorecardVariableCategory[];

  type: ScorecardVariableCategoryType;

  constructor(props: ScorecardVariable) {
    this.name = props.name;
    this.categories = props.categories;
    this.type = props.type;
  }
}

/**
 *  ***DEPRECATED***
 * Configuration class for a scorecard.
 * Holds the version of the scorecard and an array of ScorecardVariables,
 * defining the structure and criteria of the scorecard.
 */
export class ScorecardConfig {
  version: ScorecardVersion;

  variables: ScorecardVariable[];

  minScore: number;

  maxScore: number;

  minScaledScore: number;

  maxScaledScore: number;

  constructor(props: ScorecardConfig) {
    this.version = props.version;
    this.variables = props.variables;
    this.minScore = props.minScore;
    this.maxScore = props.maxScore;
    this.minScaledScore = props.minScaledScore;
    this.maxScaledScore = props.maxScaledScore;
  }
}

/**
 * *** DEPRECATED ***
 * Represents the detailed selection of a category for a specific variable
 * in the scorecard during a risk analysis.
 * Stores the chosen variable and the corresponding category details.
 */
export class ScorecardDetail {
  variable: ScorecardVariableName;

  category: ScorecardDetailCategory;

  constructor(props: ScorecardDetail) {
    this.variable = props.variable;
    this.category = props.category;
  }
}

/**
 * *** DEPRECATED ***
 * Represents the overall scorecard used in a risk analysis.
 * Includes the total score achieved, the version of the scorecard used,
 * and a detailed breakdown of the score in terms of individual variables and categories.
 */
export class Scorecard {
  totalScore: number;

  scaledScore: number;

  minScore: number;

  maxScore: number;

  minScaledScore: number;

  maxScaledScore: number;

  details: ScorecardDetail[];

  constructor(props: Scorecard) {
    this.totalScore = props.totalScore;
    this.details = props.details;
    this.scaledScore = props.scaledScore;
    this.minScore = props.minScore;
    this.maxScore = props.maxScore;
    this.minScaledScore = props.minScaledScore;
    this.maxScaledScore = props.maxScaledScore;
  }
}

/**
 * *** DEPRECATED ***
 * Represents the complete risk analysis for an individual or entity.
 * Includes the status of the analysis and the detailed Scorecard used,
 * capturing the total score and the specific assessments made for each variable.
 */
export class RiskAnalysis {
  status: AnalysisStatus;

  scorecardVersion: ScorecardVersion;

  scorecard?: Scorecard | null;

  constructor(props: RiskAnalysis) {
    this.status = props.status;
    this.scorecardVersion = props.scorecardVersion;
    this.scorecard = props.scorecard || null;
  }
}

export type RiskAnalysisDataVariables = Partial<Map<ScorecardVariableName, number | string>>;

/**
 * ***DEPRECATED***
 * Represents the data collected for a risk analysis.
 * We store all the variables as they become available and are calculated.
 * Then we use this data to generate the scorecard and perform the risk analysis.
 *
 */
export class RiskAnalysisData {
  id?: string | undefined;

  requestId: string;

  variables: RiskAnalysisDataVariables;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: RiskAnalysisData) {
    this.id = props.id;
    this.requestId = props.requestId;
    this.variables = props.variables;
    this.createdAt = props.createdAt || new Date();
    this.updatedAt = props.updatedAt || new Date();
  }
}

export class PalencaJobRetrieveEarnings {
  accountId: string;

  platform: GigPlatform;

  requestId: string;

  constructor(props: PalencaJobRetrieveEarnings) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.requestId = props.requestId;
  }
}

export class PalencaJobRetrieveMetrics {
  accountId: string;

  platform: GigPlatform;

  requestId: string;

  constructor(props: PalencaJobRetrieveMetrics) {
    this.accountId = props.accountId;
    this.platform = props.platform;
    this.requestId = props.requestId;
  }
}

export class FileUploadRetries {
  requestId: string;

  platform: string;

  uploadType: string;

  fileMetaData: {
    originalName: string;
    mimeType: string;
    contentLength: number;
  };

  count: number;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: FileUploadRetries) {
    this.requestId = props.requestId;
    this.platform = props.platform;
    this.uploadType = props.uploadType;
    this.fileMetaData = props.fileMetaData;
    this.count = props.count;
  }
}

export class RequestAvalData {
  name?: string | null;

  phone?: string | null;

  email?: string | null;

  location?: string | null;

  constructor(props: RequestAvalData) {
    this.name = props.name;
    this.phone = props.phone;
    this.email = props.email;
    this.location = props.location;
  }
}

export class RequestLocationData {
  latitude?: number | null;

  longitude?: number | null;

  location?: string | null;

  ipAddress?: string | null;

  isFromIP?: boolean | null;

  constructor(props: RequestLocationData) {
    this.latitude = props.latitude;
    this.longitude = props.longitude;
    this.location = props.location;
    this.ipAddress = props.ipAddress;
    this.isFromIP = props.isFromIP;
  }
}

export class RequestIpInfoData {
  ip?: string | null;

  hostname?: string | null;

  city?: string | null;

  region?: string | null;

  country?: string | null;

  loc?: string | null;

  org?: string | null;

  postal?: string | null;

  timezone?: string | null;

  constructor(props: RequestIpInfoData) {
    this.ip = props.ip;
    this.hostname = props.hostname;
    this.city = props.city;
    this.region = props.region;
    this.country = props.country;
    this.loc = props.loc;
    this.org = props.org;
    this.postal = props.postal;
    this.timezone = props.timezone;
  }
}
