import xlsx from 'xlsx';
import fs from 'fs';
import path from 'path';

/**
 * Compara los contratos del pending.xlsx con los archivos PDF generados en la carpeta pagares
 * y muestra cuáles faltan.
 */
// getMissingPagareFiles();
export async function getMissingPagareFiles() {
  const root = process.cwd();
  const filePath = path.join(root, 'pending.xlsx');
  const pagaresFolderPath = path.join(root, 'pagares');

  // Leer contratos del Excel
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = xlsx.utils.sheet_to_json(worksheet, { raw: false });

  // Obtener todos los contractNumbers del Excel
  const carNumbersToUpdate = data.map(
    (item: any) => item.Contrato.toString().replace(',', '').replace('.', '-').split('-')[0]
  );

  // Leer archivos en la carpeta pagares
  const files = fs.readdirSync(pagaresFolderPath);

  // Extraer contractNumbers de los nombres de archivo
  const contractNumbersInFolder = files
    .map((filename) => {
      // El nombre esperado es: contractNumber-...pdf
      const match = filename.match(/^([^-]+)-/);
      return match ? match[1] : null;
    })
    .filter(Boolean);

  // Filtrar los contractNumbers que no tienen archivo PDF
  const missing = carNumbersToUpdate.filter((carNumber) => !contractNumbersInFolder.includes(carNumber));

  console.log('Total en Excel:', carNumbersToUpdate.length);
  console.log('Total archivos PDF:', contractNumbersInFolder.length);
  console.log('Faltan:', missing.length);
  console.log('Contratos faltantes:', missing);

  // Opcional: guardar en un archivo
  fs.writeFileSync(path.join(root, 'faltantes-pagares.json'), JSON.stringify(missing, null, 2));
}
