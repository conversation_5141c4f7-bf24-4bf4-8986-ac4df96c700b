import fcmSchema from '@/models/fcmSchema';
import { AlreadyExistException, NotFoundException } from '@/clean/errors/exceptions';
import { fcmNotificationService } from '@/modules/FirebaseCloudMessaging/services/fcmNotification.service';

jest.mock('@/models/fcmSchema', () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  updateOne: jest.fn(),
  deleteOne: jest.fn(),
}));

jest.mock('@/clean/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

const mockSendEach = jest.fn();

jest.mock('@/modules/FirebaseCloudMessaging/configuration/firebaseAdmin', () => ({
  firebaseAdmin: {
    messaging: () => ({
      sendEach: mockSendEach,
    }),
  },
}));

describe('FCMNotificationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('registerNotificationToken', () => {
    it('should create a new FCM token entry if none exists', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue(null),
      });
      (fcmSchema.create as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.registerNotificationToken(
        'id',
        'token',
        new Map([
          ['os', 'android'],
          ['version', '23.0'],
        ])
      );

      expect(fcmSchema.create).toHaveBeenCalledWith({
        associateId: 'id',
        fcmTokens: [
          expect.objectContaining({
            token: 'token',
            isActive: true,
            deviceDetails: new Map([
              ['os', 'android'],
              ['version', '23.0'],
            ]),
          }),
        ],
      });
    });

    it('should throw AlreadyExistException if token already exists and in active state', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token', isActive: true }],
        }),
      });

      await expect(
        fcmNotificationService.registerNotificationToken(
          'id',
          'token',
          new Map([
            ['os', 'android'],
            ['version', '23.0'],
          ])
        )
      ).rejects.toThrow(AlreadyExistException);
    });

    it('should add token if not already present', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'other-token' }],
        }),
      });
      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({});

      await fcmNotificationService.registerNotificationToken(
        'id',
        'token',
        new Map([
          ['os', 'android'],
          ['version', '23.0'],
        ])
      );

      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { associateId: 'id' },
        expect.objectContaining({
          $push: expect.any(Object),
        })
      );
    });

    it('should update token to active if it exists but is inactive', async () => {
      (fcmSchema.findOne as jest.Mock).mockReturnValue({
        lean: jest.fn().mockResolvedValue({
          fcmTokens: [{ token: 'token', isActive: false }],
        }),
      });

      (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });
      await fcmNotificationService.registerNotificationToken(
        'id',
        'token',
        new Map([
          ['os', 'android'],
          ['version', '23.0'],
        ])
      );
      expect(fcmSchema.updateOne).toHaveBeenCalledWith(
        { associateId: 'id', 'fcmTokens.token': 'token' },
        {
          $set: {
            'fcmTokens.$.isActive': true,
            'fcmTokens.$.updatedAt': expect.any(Date),
          },
        }
      );
    });

    describe('unregisterNotificationToken', () => {
      it('should unregister token if found', async () => {
        (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });

        await fcmNotificationService.unregisterNotificationToken('id', 'token');

        expect(fcmSchema.updateOne).toHaveBeenCalledWith(
          { associateId: 'id', 'fcmTokens.token': 'token' },
          { $pull: { fcmTokens: { token: 'token' } } }
        );
      });

      it('should throw NotFoundException if token not found', async () => {
        (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 });

        await expect(fcmNotificationService.unregisterNotificationToken('id', 'token')).rejects.toThrow(
          NotFoundException
        );
      });
    });

    describe('updateNotificationTokenState', () => {
      it('should update token state if found', async () => {
        (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 1 });

        await fcmNotificationService.updateNotificationTokenState('id', 'token', false);

        expect(fcmSchema.updateOne).toHaveBeenCalledWith(
          { associateId: 'id', 'fcmTokens.token': 'token' },
          { $set: { 'fcmTokens.$.isActive': false } }
        );
      });

      it('should throw NotFoundException if token not found', async () => {
        (fcmSchema.updateOne as jest.Mock).mockResolvedValue({ modifiedCount: 0 });

        await expect(
          fcmNotificationService.updateNotificationTokenState('id', 'token', false)
        ).rejects.toThrow(NotFoundException);
      });
    });

    describe('FCMNotificationService - sendNotificationToAssociateById', () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      const payload = {
        title: 'Test Title',
        body: 'Test Body',
        data: { foo: 'bar' },
      };

      it('should throw NotFoundException if no FCM tokens found', async () => {
        (fcmSchema.findOne as jest.Mock).mockReturnValue({
          lean: jest.fn().mockResolvedValue(null),
        });

        await expect(fcmNotificationService.sendNotificationToAssociateById('id', payload)).rejects.toThrow(
          NotFoundException
        );
      });

      it('should throw NotFoundException if no active tokens', async () => {
        (fcmSchema.findOne as jest.Mock).mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            fcmTokens: [{ token: 'token1', isActive: false }],
          }),
        });

        await expect(fcmNotificationService.sendNotificationToAssociateById('id', payload)).rejects.toThrow(
          NotFoundException
        );
      });

      it('should call sendEach with active tokens and log success', async () => {
        (fcmSchema.findOne as jest.Mock).mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            fcmTokens: [
              { token: 'token1', isActive: true, deviceDetails: 'iPhone' },
              { token: 'token2', isActive: false, deviceDetails: 'Android' },
              { token: 'token3', isActive: true, deviceDetails: 'Web' },
            ],
          }),
        });

        mockSendEach.mockResolvedValue({
          responses: [{ success: true }, { success: true }],
        });

        await fcmNotificationService.sendNotificationToAssociateById('id', payload);

        expect(mockSendEach).toHaveBeenCalledWith([
          {
            token: 'token1',
            notification: { title: 'Test Title', body: 'Test Body' },
            data: { foo: 'bar' },
          },
          {
            token: 'token3',
            notification: { title: 'Test Title', body: 'Test Body' },
            data: { foo: 'bar' },
          },
        ]);
      });

      it('should log error if sendEach throws', async () => {
        (fcmSchema.findOne as jest.Mock).mockReturnValue({
          lean: jest.fn().mockResolvedValue({
            fcmTokens: [{ token: 'token1', isActive: true, deviceDetails: 'iPhone' }],
          }),
        });

        mockSendEach.mockRejectedValue(new Error('FCM error'));

        await fcmNotificationService.sendNotificationToAssociateById('id', payload);
      });
    });
  });
});
