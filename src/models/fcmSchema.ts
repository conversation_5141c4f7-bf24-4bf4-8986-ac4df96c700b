import { model, Schema, Types } from 'mongoose';

// Sub-schema for fcmTokens with timestamps
const fcmTokenSchema = new Schema(
  {
    token: { type: String, required: true, unique: true },
    isActive: { type: Boolean, required: true },
    deviceDetails: { type: Object, required: true },
  },
  { _id: false, timestamps: true }
);

export interface FCMNotificationMongoI {
  _id: Types.ObjectId;
  associateId: Types.ObjectId;
  fcmTokens: Array<{
    token: string;
    isActive: boolean;
    deviceDetails: Record<string, any>;
    createdAt?: Date;
    updatedAt?: Date;
  }>;
}

const fcmSchema = new Schema<FCMNotificationMongoI>(
  {
    associateId: {
      type: Schema.Types.ObjectId,
      ref: 'Associate',
      required: [true, 'associateId is required'],
    },
    fcmTokens: [fcmTokenSchema],
  },
  { timestamps: true }
);

const FCM = model('FCMNotification', fcmSchema);

const instance = new FCM();

export type FCMType = typeof instance;

export default FCM as FCMType & typeof FCM;
