import { Router } from 'express';
import {
  getAdmissionRequests,
  blockLeadsAssignation,
  getBlockLeadAssignation,
  deleteBlockLeadAssignment,
  updateBlockLeadAssignation,
  getBlockLeadAssignationAuditLogs,
} from '../controllers/leadAssignment';

const leadAssignment = Router();

leadAssignment.get('/search', getAdmissionRequests);
leadAssignment.post('/block-leads', blockLeadsAssignation);
leadAssignment.get('/block-leads-list', getBlockLeadAssignation);
leadAssignment.delete('/block-leads/:id/:userId', deleteBlockLeadAssignment);
leadAssignment.put('/block-leads/:id', updateBlockLeadAssignation);
leadAssignment.get('/block-lead/audit-logs', getBlockLeadAssignationAuditLogs);

export default leadAssignment;
