# Wire4EasyAccounts Entity Documentation

## Overview

The `Wire4EasyAccounts` entity is a MongoDB model that manages simplified bank account creation and management through the Wire4 payment platform. This entity serves as a bridge between OneCarNow associates and their corresponding CLABE (Clave Bancaria Estandarizada) accounts for electronic payments in Mexico.

## Entity Structure

### Schema Definition

```typescript
const wire4easyAccountsSchema = new Schema({
  associateEmail: {
    type: String,
    required: [true, 'Object is required'],
  },
  clabe: {
    type: String,
    required: [true, 'Api version is required'],
  },
  contract: {
    type: String,
    required: [true, 'Api version is required'],
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
```

### Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `associateEmail` | String | Yes | Email address of the associate linked to this account |
| `clabe` | String | Yes | CLABE (Clave Bancaria Estandarizada) - Mexican standardized bank code |
| `contract` | String | Yes | Contract identifier or alias for the account |
| `createdAt` | Date | No | Timestamp when the record was created (auto-generated) |
| `updatedAt` | Date | No | Timestamp when the record was last updated (auto-generated) |

## Business Logic

### Purpose

The Wire4EasyAccounts entity facilitates:

1. **Simplified Account Creation**: Streamlined process for creating Wire4 bank accounts
2. **Associate-Account Mapping**: Links OneCarNow associates to their payment accounts
3. **Payment Processing**: Enables electronic payments through the Wire4 platform
4. **Contract Management**: Associates accounts with specific contracts or vehicle identifiers

### Key Features

- **Easy Account Setup**: Simplified account creation process compared to standard Wire4 accounts
- **Email-Based Identification**: Uses associate email as the primary identifier
- **Contract Linking**: Associates accounts with specific contracts for better organization
- **Automatic Timestamps**: Tracks creation and modification times

## API Endpoints

### Create Easy Account

**Endpoint**: `POST /wire4/createEasyAccount`

**Authentication**: Required (Bearer Token)

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "nombre": "John Doe",
  "contrato": "CONTRACT123"
}
```

**Response**:
```json
{
  "clabe": "646180157000000004",
  "alias": "CONTRACT123",
  "email": ["<EMAIL>"],
  "name": "John Doe"
}
```

**Process Flow**:
1. Validates required fields (email, nombre, contrato)
2. Creates Wire4 bank account via external API
3. Updates AssociatePayments with the new CLABE
4. Creates Wire4EasyAccounts record
5. Returns account details

## Related Entities

### AssociatePayments
- **Relationship**: One-to-One
- **Connection**: `associateEmail` field links to AssociatePayments record
- **Purpose**: Stores the CLABE in the `monexClabe` field for payment processing

### Associates
- **Relationship**: One-to-One (indirect)
- **Connection**: Through `associateEmail` field
- **Purpose**: Provides associate details for account creation

### StockVehicle
- **Relationship**: One-to-Many (indirect)
- **Connection**: Through contract/vehicle number
- **Purpose**: Links accounts to specific vehicles for payment tracking

## Integration Points

### Wire4 Platform Integration

The entity integrates with Wire4's external API for:

- **Account Creation**: Creates depositant accounts in Wire4 system
- **Token Management**: Uses OAuth2 tokens for API authentication
- **Subscription Management**: Works with different Wire4 subscriptions (OneCarNow, i80)

### Payment Processing

- **SPEI Transactions**: Enables Mexican electronic payment system integration
- **Webhook Processing**: Receives payment notifications from Wire4
- **Payment Tracking**: Links payments to specific accounts and contracts

## Usage Examples

### Creating an Easy Account

```typescript
// Controller implementation
export const createEasyAccount: AsyncController = async (req, res) => {
  const { email, nombre, contrato } = req.body;
  
  // Validate input
  if (!email || !nombre || !contrato) {
    return res.status(500).json({ error: 'Missing required fields' });
  }

  // Prepare Wire4 API data
  const userData = JSON.stringify({
    alias: contrato,
    currency_code: 'MXN',
    email: [email],
    name: nombre,
  });

  try {
    // Create account in Wire4
    const response = await createWire4BankAccount(userData);
    
    // Update associate payments
    await AssociatePayments.findOneAndUpdate(
      { associateEmail: email },
      { monexClabe: response.clabe },
      { new: true }
    );

    // Create Wire4EasyAccounts record
    const wire4Account = {
      associateEmail: email,
      clabe: response.clabe,
      contract: contrato,
    };
    await Wire4EasyAccounts.create(wire4Account);

    return res.status(200).json(response);
  } catch (error) {
    console.error('ERROR', error);
    return res.status(500).json({ error: 'Account creation failed' });
  }
};
```

### Querying Accounts

```typescript
// Find account by email
const account = await Wire4EasyAccounts.findOne({ 
  associateEmail: '<EMAIL>' 
});

// Find accounts by contract
const contractAccounts = await Wire4EasyAccounts.find({ 
  contract: 'CONTRACT123' 
});
```

## Error Handling

### Common Error Scenarios

1. **Missing Required Fields**: Returns 500 status with missing body error
2. **Wire4 API Failures**: Logs error and returns generic failure message
3. **Database Errors**: Catches and logs database operation failures
4. **Duplicate Accounts**: Handled through existing CLABE checks in AssociatePayments

### Error Response Format

```json
{
  "error": "Error message description"
}
```

## Security Considerations

### Authentication
- All endpoints require valid Bearer token authentication
- Token verification through `verifyToken` middleware

### Data Protection
- Email addresses are stored as provided (case-sensitive)
- CLABE numbers are generated by Wire4 and stored securely
- No sensitive financial data is stored beyond CLABE identifiers

## Performance Considerations

### Database Indexing
- Consider indexing on `associateEmail` for faster lookups
- Index on `clabe` for payment processing queries
- Compound index on `associateEmail` + `contract` for complex queries

### API Rate Limiting
- Wire4 API calls are subject to external rate limits
- Implement retry logic for failed API calls
- Cache tokens to reduce authentication overhead

## Monitoring and Logging

### Key Metrics to Monitor
- Account creation success/failure rates
- Wire4 API response times
- Database query performance
- Payment processing volumes

### Logging Points
- Account creation attempts and results
- Wire4 API errors and responses
- Database operation failures
- Authentication failures

## Future Enhancements

### Potential Improvements
1. **Batch Account Creation**: Support for creating multiple accounts simultaneously
2. **Account Status Tracking**: Add status field to track account lifecycle
3. **Enhanced Error Handling**: More specific error codes and messages
4. **Audit Trail**: Track account modifications and access patterns
5. **Account Validation**: Implement CLABE format validation
6. **Soft Deletion**: Add deletion tracking instead of hard deletes

### Integration Opportunities
1. **Real-time Notifications**: WebSocket integration for account status updates
2. **Analytics Dashboard**: Account creation and usage metrics
3. **Automated Reconciliation**: Periodic validation against Wire4 records
4. **Multi-currency Support**: Extend beyond MXN for international operations
