# Wire4PaymentsSchema Entity Documentation

## Overview

The `Wire4PaymentsSchema` entity is a MongoDB model that stores detailed payment transaction records from the Wire4 platform. This entity captures comprehensive information about SPEI (Sistema de Pagos Electrónicos Interbancarios) transactions, including sender details, recipient information, amounts, and transaction metadata. It serves as the primary record for all incoming payments processed through the Wire4 payment gateway.

## Entity Structure

### Schema Definition

```typescript
const wire4PaymentsSchema = new Schema({
  id: {
    type: String,
    required: [true, 'Id is required'],
  },
  object: {
    type: String,
    required: [true, 'Object is required'],
  },
  api_version: {
    type: String,
    required: [true, 'Api version is required'],
  },
  created: {
    type: Date,
    required: [true, 'Created is required'],
  },
  data: {
    beneficiary_account: {
      type: String,
      required: [true, 'Beneficiary account is required'],
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
    },
    currency_code: {
      type: String,
      required: [true, 'Currency code is required'],
    },
    deposit_date: {
      type: Date,
      required: [true, 'Deposit date is required'],
    },
    confirm_date: {
      type: Date,
      required: [true, 'Confirm date is required'],
    },
    depositant: {
      type: String,
      required: [true, 'Depositant is required'],
    },
    depositant_clabe: {
      type: String,
      required: [true, 'Depositant clabe is required'],
    },
    depositant_email: {
      type: String,
      required: [true, 'Depositant email is required'],
    },
    depositant_rfc: {
      type: String,
      required: [true, 'Depositant rfc is required'],
    },
    monex_description: {
      type: String,
      required: [true, 'Monex description is required'],
    },
    monex_transaction_id: {
      type: String,
      required: [true, 'Monex transaction id is required'],
    },
    sender_account: {
      type: String,
      required: [true, 'Sender account is required'],
    },
    sender_name: {
      type: String,
      required: [true, 'Sender name is required'],
    },
    sender_rfc: {
      type: String,
      required: [true, 'Sender rfc is required'],
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
    },
    reference: {
      type: String,
      required: [true, 'Reference is required'],
    },
    clave_rastreo: {
      type: String,
      required: [true, 'Clave rastreo is required'],
    },
  },
  livemode: {
    type: Boolean,
    required: [true, 'Livemode is required'],
  },
  pending_webhooks: {
    type: Number,
    required: [true, 'Pending webhooks is required'],
  },
  type: {
    type: String,
    required: [true, 'Type is required'],
  },
  createdAt: {
    type: Date,
    default: getCurrentDateObject,
  },
  updatedAt: {
    type: Date,
    default: getCurrentDateObject,
  },
});
```

### Properties

#### Root Level Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `id` | String | Yes | Unique identifier from Wire4 platform |
| `object` | String | Yes | Object type identifier (e.g., "spei_incoming") |
| `api_version` | String | Yes | Wire4 API version used for the transaction |
| `created` | Date | Yes | Timestamp when the transaction was created in Wire4 |
| `livemode` | Boolean | Yes | Indicates if transaction was in production (true) or test mode (false) |
| `pending_webhooks` | Number | Yes | Number of pending webhook notifications |
| `type` | String | Yes | Transaction type classification |
| `createdAt` | Date | No | Local timestamp when record was created (auto-generated) |
| `updatedAt` | Date | No | Local timestamp when record was last updated (auto-generated) |

#### Data Object Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `beneficiary_account` | String | Yes | Recipient's bank account number |
| `amount` | Number | Yes | Transaction amount in the specified currency |
| `currency_code` | String | Yes | Currency code (typically "MXN" for Mexican Peso) |
| `deposit_date` | Date | Yes | Date when the deposit was made |
| `confirm_date` | Date | Yes | Date when the transaction was confirmed |
| `depositant` | String | Yes | Name of the account holder receiving the payment |
| `depositant_clabe` | String | Yes | CLABE of the receiving account |
| `depositant_email` | String | Yes | Email address of the account holder |
| `depositant_rfc` | String | Yes | RFC (tax ID) of the account holder |
| `monex_description` | String | Yes | Description provided by Monex (banking partner) |
| `monex_transaction_id` | String | Yes | Unique transaction ID from Monex |
| `sender_account` | String | Yes | Sender's bank account number |
| `sender_name` | String | Yes | Name of the person/entity sending the payment |
| `sender_rfc` | String | Yes | RFC (tax ID) of the sender |
| `description` | String | Yes | Transaction description or concept |
| `reference` | String | Yes | Payment reference number |
| `clave_rastreo` | String | Yes | Tracking key for the SPEI transaction |

## Business Logic

### Purpose

The Wire4PaymentsSchema entity serves multiple critical functions:

1. **Transaction Recording**: Stores complete payment transaction details from Wire4 webhooks
2. **Payment Reconciliation**: Enables matching payments to specific accounts and contracts
3. **Audit Trail**: Maintains comprehensive records for financial auditing
4. **Payment Processing**: Triggers automated payment processing workflows
5. **Reporting**: Provides data for financial reports and analytics

### Key Features

- **Webhook Integration**: Automatically populated via Wire4 webhook notifications
- **Complete Transaction Data**: Captures all relevant payment information
- **SPEI Compliance**: Follows Mexican electronic payment system standards
- **Automatic Processing**: Triggers payment matching and account updates
- **JSON Transformation**: Custom serialization for API responses

## Webhook Integration

### Payment Webhook Processing

The entity is primarily populated through webhook notifications from Wire4:

```typescript
export const webhook: AsyncController = async (req, res) => {
  // Store webhook data
  const webhookData = new Wire4Webhook({ body: req.body });
  await webhookData.save();
  
  const { object, data } = req.body;
  
  if (object === 'spei_incoming') {
    // Create payment record
    await Wire4Payment.create(req.body);
    
    // Process payment matching logic
    if (data.depositant_clabe) {
      const gigPayments = await GigPayments.find({
        'body.client.metadata.clabe': data.depositant_clabe,
        isPaid: false,
      }).sort({ createdAt: -1 });
      
      // Handle payment processing...
    }
  }
  
  return res.status(200).json({});
};
```

## API Endpoints

### Get SPEI Incoming Transactions

**Endpoint**: `GET /wire4/get-spei-incoming/:beginDate/:endDate`

**Authentication**: Required (Bearer Token)

**Parameters**:
- `beginDate`: Start date for transaction search (YYYY-MM-DD format)
- `endDate`: End date for transaction search (YYYY-MM-DD format)

**Response**: Array of SPEI incoming transactions from Wire4 API

### Find Payment by Reference

**Endpoint**: `GET /wire4/find-payment-by-reference/:reference`

**Authentication**: Required (Bearer Token)

**Parameters**:
- `reference`: Payment reference number to search for

**Response**:
```json
[
  {
    "body": {
      "data": {
        "reference": "REF123456",
        "amount": 1500.00,
        "depositant": "John Doe",
        "depositant_clabe": "646180157000000004",
        "depositant_email": "<EMAIL>"
      }
    }
  }
]
```

### Find Payment by Description

**Endpoint**: `GET /wire4/find-payment-by-concept/:description`

**Authentication**: Required (Bearer Token)

**Parameters**:
- `description`: Payment description/concept to search for

### Find Payment by Tracking Key

**Endpoint**: `GET /wire4/find-payment-by-clave-rastreo/:claveRastreo`

**Authentication**: Required (Bearer Token)

**Parameters**:
- `claveRastreo`: SPEI tracking key to search for

### Get All Recent Payments

**Endpoint**: `GET /wire4/get-all-payments`

**Authentication**: Required (Bearer Token)

**Response**: Aggregated payment data from the last 15 days with selected fields

## Related Entities

### Wire4Webhook
- **Relationship**: One-to-One
- **Connection**: Raw webhook data storage
- **Purpose**: Stores complete webhook payload for audit and debugging

### GigPayments
- **Relationship**: One-to-Many
- **Connection**: Links payments to specific payment requests
- **Purpose**: Matches incoming payments to pending payment orders

### AssociatePayments
- **Relationship**: One-to-Many (indirect)
- **Connection**: Through `depositant_clabe` field
- **Purpose**: Links payments to associate accounts

### Wire4EasyAccounts
- **Relationship**: One-to-Many (indirect)
- **Connection**: Through `depositant_clabe` field
- **Purpose**: Associates payments with easy account holders

## Payment Processing Workflow

### Automatic Payment Matching

When a payment webhook is received:

1. **Record Creation**: Payment data is stored in Wire4PaymentsSchema
2. **Account Matching**: System searches for matching CLABE in GigPayments
3. **Amount Validation**: Compares payment amount with expected amount
4. **Tolerance Checking**: Allows for small discrepancies (±0.5 MXN) and overpayments (up to 20 MXN)
5. **Payment Confirmation**: Updates payment status and triggers confirmations
6. **Wallet Updates**: Updates associate wallet balances
7. **Notifications**: Sends confirmation messages to relevant parties

### Payment Tolerance Logic

```typescript
const permitCents = 0.5;
const permitOverpaying = 20;

// Exact match or within tolerance
if (gigPayment.body.total === data.amount || 
    Math.abs(gigPayment.body.total - data.amount) <= permitCents) {
  // Process exact payment
}

// Overpayment within limits
if (data.amount > gigPayment.body.total && 
    data.amount - gigPayment.body.total <= permitOverpaying) {
  // Process overpayment
}
```

## JSON Transformation

The schema includes custom JSON transformation to clean up API responses:

```typescript
wire4PaymentsSchema.set('toJSON', {
  transform: (_document: any, returnedObject: any) => {
    returnedObject.id = returnedObject._id.toString();
    delete returnedObject._id;
    delete returnedObject.__v;
    delete returnedObject.createdAt;
  },
});
```

This transformation:
- Converts MongoDB `_id` to `id` string
- Removes MongoDB-specific fields (`_id`, `__v`)
- Removes internal `createdAt` timestamp

## Usage Examples

### Creating Payment Record from Webhook

```typescript
// Webhook handler automatically creates payment records
await Wire4Payment.create(req.body);
```

### Querying Payments

```typescript
// Find payments by reference
const payments = await Wire4Payment.find({
  'data.reference': 'REF123456'
});

// Find payments by CLABE
const clabePayments = await Wire4Payment.find({
  'data.depositant_clabe': '646180157000000004'
});

// Find payments by amount
const amountPayments = await Wire4Payment.find({
  'data.amount': 1500.00
});

// Find payments within date range
const dateRangePayments = await Wire4Payment.find({
  'data.deposit_date': {
    $gte: new Date('2024-01-01'),
    $lte: new Date('2024-01-31')
  }
});
```

### Aggregation Queries

```typescript
// Get payment summary for last 15 days
const paymentSummary = await Wire4Payment.aggregate([
  { 
    $match: { 
      createdAt: { $gte: moment().subtract(15, 'days').toDate() } 
    } 
  },
  { 
    $group: {
      _id: '$data.depositant_clabe',
      totalAmount: { $sum: '$data.amount' },
      paymentCount: { $sum: 1 },
      lastPayment: { $max: '$data.deposit_date' }
    }
  }
]);
```

## Error Handling

### Common Error Scenarios

1. **Missing Required Fields**: Webhook data validation failures
2. **Duplicate Transactions**: Handling repeated webhook notifications
3. **Invalid Amounts**: Non-numeric or negative amounts
4. **Date Format Issues**: Invalid date formats in webhook data
5. **CLABE Validation**: Invalid CLABE format or length

### Error Response Format

```json
{
  "error": "Error message description"
}
```

## Security Considerations

### Data Protection
- All financial data is encrypted at rest
- PII (personally identifiable information) is handled according to privacy regulations
- RFC and account numbers are stored securely
- Access is restricted through authentication middleware

### Webhook Security
- Webhook endpoints should validate Wire4 signatures
- Rate limiting on webhook endpoints
- Duplicate transaction detection and prevention

## Performance Considerations

### Database Indexing

Recommended indexes for optimal performance:

```javascript
// Compound index for payment searches
db.wire4paymentschemas.createIndex({
  "data.depositant_clabe": 1,
  "data.deposit_date": -1
});

// Reference lookup index
db.wire4paymentschemas.createIndex({
  "data.reference": 1
});

// Tracking key index
db.wire4paymentschemas.createIndex({
  "data.clave_rastreo": 1
});

// Amount and date range queries
db.wire4paymentschemas.createIndex({
  "data.amount": 1,
  "createdAt": -1
});
```

### Query Optimization
- Use projection to limit returned fields for large datasets
- Implement pagination for payment listing endpoints
- Cache frequently accessed payment data
- Use aggregation pipelines for complex reporting queries

## Monitoring and Logging

### Key Metrics to Monitor
- Payment processing success/failure rates
- Webhook processing latency
- Payment matching accuracy
- Database query performance
- Storage growth rates

### Logging Points
- Webhook receipt and processing
- Payment matching results
- Failed payment processing attempts
- Database operation performance
- Security-related events

## Future Enhancements

### Potential Improvements
1. **Payment Status Tracking**: Add status field to track payment lifecycle
2. **Batch Processing**: Support for processing multiple payments simultaneously
3. **Enhanced Validation**: Implement comprehensive data validation rules
4. **Payment Categories**: Add categorization for different payment types
5. **Automated Reconciliation**: Implement automated bank reconciliation
6. **Real-time Notifications**: WebSocket integration for real-time payment updates
7. **Payment Analytics**: Advanced reporting and analytics capabilities
8. **Multi-currency Support**: Extend beyond MXN for international payments

### Integration Opportunities
1. **Accounting Systems**: Integration with external accounting platforms
2. **Tax Reporting**: Automated tax report generation
3. **Fraud Detection**: Machine learning-based fraud detection
4. **Payment Scheduling**: Support for scheduled and recurring payments
5. **Mobile Notifications**: Push notifications for payment events
